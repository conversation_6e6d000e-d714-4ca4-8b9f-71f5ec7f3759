FROM golang:1.22 AS build
WORKDIR /app

COPY ./src ./src
COPY ./go.mod ./go.mod
COPY ./go.sum ./go.sum
RUN go env -w GO111MODULE=on
RUN go env -w GOPROXY=https://proxy.golang.org,direct

RUN go env -w GOPRIVATE=dev.azure.com
RUN git config --global url."https://Kelvin:<EMAIL>".insteadOf "https://dev.azure.com"

RUN rm -rf $(go env GOPATH)/pkg/mod
#RUN go build -o ./bin/ctint-cdss-message-app ./src
RUN go mod download && go build -ldflags="-s -w" -o ./bin/ctint-cdss-message-app ./src

FROM ubuntu:22.04
WORKDIR /app

RUN mkdir microservices-config
RUN mkdir global-config
RUN mkdir logs
COPY --from=build /app/bin/ctint-cdss-message-app /app/bin/ctint-cdss-message-app

WORKDIR /app/bin
EXPOSE 8205

ENTRYPOINT [ "./ctint-cdss-message-app"]