apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-cdss-message-app-deployment
  namespace: ctint-qhms-phase2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-cdss-message-app-deployment
  template:
    metadata:
      labels:
        app: ctint-cdss-message-app-deployment
    spec:
      containers:
      - name: ctint-cdss-message-app
        image: cdss3projectdevacr.azurecr.io/qhms2-cdss-message:1.0.4194
        imagePullPolicy: Always
        args: ["uat"]
        ports:
        - containerPort: 8205
        volumeMounts:
        - name: ctint-microservices-config
          mountPath: /app/microservices-config
        - name: ctint-global-config
          mountPath: /app/global-config
        - name: ctint-cdss-message-logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "128Mi"
            cpu: "250m"
          limits:
            memory: "256Mi"
            cpu: "500m"
      volumes:
      - name: ctint-microservices-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms2/ctint-cdss-microservices-config/ctint-cdss-message
          readOnly: false
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms2/ctint-cdss-globalconfig
          readOnly: false
      - name: ctint-cdss-message-logs
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms2/ctint-cdss-logs/ctint-cdss-message
          readOnly: false

        