apiVersion: networking.k8s.io/v1
kind: Ingress 
metadata:
  name: ctint-cdss-message-app-ingress
  namespace: ctint-qhms-phase2
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /ctint-cdss-message/$2
spec:
  ingressClassName: "nginx"
  rules:
  - http:
      paths:
      - path: /qhms2/cdss-message(/|$)(.*)
        pathType: Prefix 
        backend:
          service:
            name: ctint-cdss-message-app-service
            port:
              number: 8205

