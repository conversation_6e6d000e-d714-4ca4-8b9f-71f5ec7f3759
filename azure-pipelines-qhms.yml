trigger: none

pool:
  vmImage: 'ubuntu-latest'

variables:
  containerRegistryUrl: 'cdss3projectdevacr.azurecr.io'
  deploymentFolder: 'ctint-cdss-message'
  imageRepository: 'qhms2-cdss-message'
  tag: '1.0.$(Build.BuildId)'
  kubernetesServiceConnection: 'uat_project_aks_ctint_qhms-phase2'
  environment: 'uat_project_aks_ctint_qhms-phase2-digital'

jobs:
- job: BuildAndPushToACR
  displayName: Build and push to ACR
  steps:
  - task: Docker@2
    inputs:
      containerRegistry: 'cdss3projectdevacr'
      repository: '$(imageRepository)'
      command: 'buildAndPush'
      Dockerfile: '**/Dockerfile'
      tags: '$(tag)'

- deployment: DeployToK8s  # Change 'job' to 'deployment'
  displayName: Deploy to AKS
  dependsOn: BuildAndPushToACR
  environment: $(environment)  # Environment is valid in deployment jobs
  strategy:
    runOnce:
      deploy:
        steps:
          - checkout: self

          - script: |
              echo "Updating deployment.yml with tag $(tag)"
              sed -i 's|image: .*|image: $(containerRegistryUrl)/$(imageRepository):$(tag)|' ./k8s-qhms/$(deploymentFolder)-deployment.yaml
            displayName: Update deployment.yml with the new version

          - task: KubernetesManifest@0
            displayName: Deploy to Kubernetes cluster
            inputs:
              action: deploy
              manifests: |
                ./k8s-qhms/$(deploymentFolder)-deployment.yaml
              kubernetesServiceConnection: $(kubernetesServiceConnection)