package constants

const (
	GCMediaTypeImage string = "Image"
	GCMediaTypeVideo string = "Video"
	GCMediaTypeAudio string = "Audio"
	GCMediaTypeFile  string = "File"
	GCMediaTypeLink  string = "Link"
)

const (
	GCWebHookMessageTypeText       string = "Text"
	GCWebHookMessageTypeStructured string = "Structured"
	GCWebHookMessageTypeReceipt    string = "Receipt"
)

// GCReceiptReasonCodeEnum
const (
	MessageExpired            string = "MessageExpired"
	RateLimited               string = "RateLimited"
	MessageNotAllowed         string = "MessageNotAllowed"
	GeneralError              string = "GeneralError"
	UnsupportedMessage        string = "UnsupportedMessage"
	UnknownMessage            string = "UnknownMessage"
	InvalidMessageStructure   string = "InvalidMessageStructure"
	InvalidDestination        string = "InvalidDestination"
	ServerError               string = "ServerError"
	MediaTypeNotAllowed       string = "MediaTypeNotAllowed"
	InvalidMediaContentLength string = "InvalidMediaContentLength"
	RecipientOptedOut         string = "RecipientOptedOut"
)

// GCReceiptStatusEnum
const (
	Delivered string = "Delivered"
	Failed    string = "Failed"
	Published string = "Published"
	Sent      string = "Sent"
	Read      string = "Read"
)

const (
	MessageOutBoundStatusQueued    string = "queued"
	MessageOutBoundStatusSent      string = "sent"
	MessageOutBoundStatusFailed    string = "failed"
	MessageOutBoundStatusDelivered string = "delivered"
)

// Agentless Message Identifier 给digital proxy 认噶
const (
	GCAgentlessNormalizedMessageIdentifier       string = "DIGITAL PROXY"
	GCAgentlessWhatsAppTemplateMessageIdentifier string = "DIGITAL PROXY - WHATSAPP TEMPLATE MESSAGE"
)

const (
	GCPlatformIdentifier string = "genesys-cloud"
)

const (
	GCMessageIDWhatsAppTemplatePrefix    string = "cdss-wat"
	GCMessageIDWhatsAppInteractivePrefix string = "cdss-wat"
	GCMessageIDNormalizedPrefix          string = "cdss"
)

const (
	CDSSMessageTypeTemplate    string = "template"
	CDSSMessageTypeNormal      string = "normal"
	CDSSMessageTypeInteractive string = "interactive"
)

// GCDirectInboundType sync with ctint-digital-proxy
type GCDirectInboundType string

const (
	GCDirectInboundTypeMessagesStatus          GCDirectInboundType = "messagesStatus"
	GCDirectInboundTypeWhatsappFlowExchangeLog GCDirectInboundType = "whatsappFlowExchangeLog"
)

type ParticipantType string

const (
	ParticipantTypeIVR      ParticipantType = "ivr"
	ParticipantTypeACD      ParticipantType = "acd"
	ParticipantTypeAgent    ParticipantType = "agent"
	ParticipantTypeUser     ParticipantType = "user"
	ParticipantTypeConsult  ParticipantType = "consult"
	ParticipantTypeCustomer ParticipantType = "customer"
)

type MessageVirusScanOption int

const (
	MessageVirusScanOptionOK                        MessageVirusScanOption = 1
	MessageVirusScanOptionMessageTypeNotSupported   MessageVirusScanOption = 0
	MessageVirusScanOptionServiceDisable            MessageVirusScanOption = -1
	MessageVirusScanOptionServiceTenantNoPermission MessageVirusScanOption = -2
	MessageVirusScanOptionFileNotSupported          MessageVirusScanOption = -3
)

type JWTPlatform string

const (
	JWTPlatformWhatsApp     JWTPlatform = "whatsapp"
	JWTPlatformGenesysCloud JWTPlatform = "genesys-cloud"
	JWTPlatformEmail        JWTPlatform = "email"
)

// MessagePiiOption service : pii
type MessagePiiOption int

const (
	MessagePiiOptionOK                        MessagePiiOption = 1
	MessagePiiOptionMessageTypeNotSupported   MessagePiiOption = 0
	MessagePiiOptionServiceDisable            MessagePiiOption = -1
	MessagePiiOptionServiceTenantNoPermission MessagePiiOption = -2
	MessagePiiOptionFileNotSupported          MessagePiiOption = -3
)
