package ecs

const (
	RequestErrorCodesUnknown = iota + 1000
	RequestErrorCodesGCTokenEmpty
)

const (
	FlowErrorCodesDataActionId = iota + 1100
	FlowErrorCodesOutboundFlowMissingGlobalConfig
	FlowErrorCodesExternalFlowMissingGlobalConfig
)

const (
	OutboundErrorCodes = iota + 1200
	OutboundErrorCodesGenUUID
	OutboundErrorCodesFailedToFindReference
	OutboundErrorCodesReferencedMessageMissingOriginalPlatform
	OutboundErrorCodesReferencedMessageMissingExternalMessageId
	OutboundErrorCodesReferencedMessageStatusInvalid
	OutboundErrorCodesReferencedMessageNotInCorrectConversation
)

const (
	MessageErrorCodesFailedToCallDataAction = 10010
	MessageErrorCodesFailedToCallProvider   = 10020
	MessageErrorCodesWebHookStatusFailed    = 10030
)

const (
	UploadMediaErrorCodes = iota + 1250
	UploadMediaErrorCodesGetUploadLink
	UploadMediaErrorCodesUploadMedia
	UploadMediaErrorCodesGetUploadInfo
	UploadMediaErrorCodesDecodeBase64
)

const (
	InboundErrorCodes = iota + 1300
)

// Message conversation operation
const (
	OperationErrorCodesConversationFetch = iota + 1400
	OperationErrorCodesConversationCreation
	OperationErrorCodesConversationTransfer
	OperationErrorCodesConversationQueryFetchMKTWindow
)

// Genesys cloud error
const (
	FetchCommunicationErrorCodesGetConversationDetail = iota + 1500
	FetchCommunicationErrorCodesNoParticipantFound
	FetchCommunicationErrorCodesCannotFoundMatchedCommunicationId
	FetchCommunicationErrorCodesFoundMultipleMatchedCommunicationIds
)

// dab error
const (
	DABErrorCodesFetchNotFound = iota + 1520
	DABErrorCodesFetchFailed
	DABErrorCodesMutationFailed
)

// json error
const (
	JsonErrorCodesDecode = iota + 1530
)

// media error
const (
	MediaErrorCodesFetchNotFound = iota + 1600
	MediaErrorCodesFetchFailed
)
