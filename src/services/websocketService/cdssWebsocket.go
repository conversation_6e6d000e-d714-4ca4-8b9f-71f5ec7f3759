package websocketService

import (
	"ctint-cdss-message/src/config"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/pkg/cdssServiceUtils"
	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/config"
	"fmt"
	"github.com/kuah/cerror"
	"github.com/pkg/errors"
	"net/http"
)

func SendBroadCastMessageToUser(tenant, userId string, event string, eventData interface{}) error {
	gbConfig := commonConfig.GlobalConfigV2[tenant]
	requestUrl := *gbConfig.GetString("services.ctint-session-manager.host") + *gbConfig.GetString("services.ctint-session-manager.basepath")
	requestUrl = requestUrl + config.ProjectConfig.CDSSWSBroadCast.API
	auth := config.ProjectConfig.CDSSWSBroadCast.Auth

	if requestUrl == "" || auth == "" || tenant == "" || userId == "" || event == "" {
		// 打印全部参数
		return errors.New(fmt.Sprintf("invalid parameter for sending broadcast message to user %s : %s , %s", userId, requestUrl, auth))
	}

	eventPayload := dto.WebSocketBroadcastPayload{
		UserId:    userId,
		EventType: event,
		EventData: eventData,
	}
	// 发送请求
	resp, err := cdssServiceUtils.WSRequest(nil, tenant, http.MethodPost, requestUrl, auth, eventPayload)
	if err != nil {
		return errors.Wrap(err, "failed to send broadcast message to user")
	}
	if resp.StatusCode != http.StatusOK {
		return errors.Wrap(cerror.ErrorWithResp(resp), "received non-OK HTTP status from broadcast request")
	}
	return nil
}
