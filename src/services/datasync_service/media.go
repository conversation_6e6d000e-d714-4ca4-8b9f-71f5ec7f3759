package datasync_service

import (
	"ctint-cdss-message/src/pkg/cdssServiceUtils"
	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/config"
	"fmt"
	"github.com/kuah/cerror"
	"github.com/pkg/errors"
	"net/http"
)

func BackupMessageMedia(tenant, authorization, mediaId string) error {
	gbConfig := commonConfig.GlobalConfigV2[tenant]
	requestUrl := *gbConfig.GetString("services.ctint-datasync-hub.host") + *gbConfig.GetString("services.ctint-datasync-hub.basepath")
	requestUrl = requestUrl + fmt.Sprintf("/messages/medias/%s/patch/azure", mediaId)

	if requestUrl == "" || tenant == "" {
		// 打印全部参数
		return errors.New(fmt.Sprintf("invalid parameter for upload media : %s ", requestUrl))
	}

	// 发送请求
	resp, err := cdssServiceUtils.DSHRequest(nil, tenant, http.MethodPost, requestUrl, authorization, nil)
	if err != nil {
		return errors.Wrap(err, "failed to send broadcast message to user")
	}
	if resp.StatusCode != http.StatusOK {
		return errors.Wrap(cerror.ErrorWithResp(resp), "received non-OK HTTP status from broadcast request")
	}
	return nil
}
