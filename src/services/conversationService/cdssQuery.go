package conversationService

import (
	"context"
	"ctint-cdss-message/src/constants"
	"ctint-cdss-message/src/dab"
	dabo "ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/models/vo"
	"ctint-cdss-message/src/pkg/cdssServiceUtils"
	"ctint-cdss-message/src/services/gcService"
	"fmt"
	"net/http"
	"time"

	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/config"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/logger"
	"github.com/kuah/cerror"
	"github.com/mypurecloud/platform-client-sdk-go/v129/platformclientv2"
)

const (
	MessageConversationStateActive       string = "active"
	MessageConversationStateHeld         string = "held"
	MessageConversationStateConnected    string = "connected"
	MessageConversationStateDisconnected string = "disconnected"
	MessageConversationStateWrapup       string = "wrapup"
	MessageConversationStateAlerting     string = "alerting"
	MessageConversationStateTerminated   string = "terminated"
)

func QueryCDSSConversation(ctx context.Context, cr dto.CtintR, conversationId string, userId string) (*vo.CDSSFetchConversationDetailResp, error) {
	// send request to GC 获取conversation
	resp, err := gcService.GetMessagesConversationDetail(ctx, *cr.Tenant, conversationId, *cr.GCAuthorization)
	if err != nil {
		return nil, err
	}
	// 处理conversation结果
	var result *vo.CDSSFetchConversationDetailResp
	result, customerParticipant := ProcessMessageConversationResult(*resp, userId)
	if result.CurParticipant == nil {
		return nil, cerror.New(fmt.Sprintf("unmatched user & conversationId %s", conversationId), http.StatusUnauthorized)
	}
	// 根据integrationId获取channel
	if result.Customer != nil && result.Customer.IntegrationId != nil {
		channel, err := QueryChannelByIntegrationId(*cr.Tenant, *cr.Platform, *result.Customer.IntegrationId)
		if err != nil {
			return nil, err
		}
		result.Channel = channel
	}

	// 尝试同步conversation message
	go func() {
		// 创建一个新的context，避免原始请求context被取消影响异步操作
		asyncCtx := context.Background()
		err := TryToSyncMessages(asyncCtx, cr, conversationId, *customerParticipant.Messages, customerParticipant)
		if err != nil {
			logger.Error(fmt.Sprintf("failed to sync messages for conversation %s", conversationId))
		}
	}()

	// 当前gc 最后一条inbound的时间
	var lastGCInboundMessage *platformclientv2.Messagedetails
	if customerParticipant.Messages != nil && len(*customerParticipant.Messages) > 0 {
		customerMessages := customerParticipant.Messages
		// 获取最后一条inbound message
		lastGCInboundMessage = &(*customerMessages)[len(*customerMessages)-1]
	}

	// 根据不同的channel 处理逻辑
	if result.Channel != nil && result.Channel.RemotePlatform != nil {
		switch *result.Channel.RemotePlatform {
		case dto.CDSSMessagePlatformWhatsapp:
			// 尝试获取cdss database 中, 该手机号相关的最后message的时间做对比
			var lastMessageTime time.Time
			whatsappWindow := vo.CDSSWhatsappConversationWindow{}
			cdssLastMessage, err := QueryLastMessageByPhone(*cr.Tenant, *cr.Platform, *result.Customer.From)
			if err != nil {
				return nil, err
			}
			if lastGCInboundMessage != nil && lastGCInboundMessage.MessageTime != nil {
				lastMessageTime = (*lastGCInboundMessage.MessageTime).UTC()
			}
			if cdssLastMessage != nil && cdssLastMessage.Timestamp != nil {
				// 拿最后的
				if cdssLastMessage.Timestamp.After(lastMessageTime) {
					lastMessageTime = *cdssLastMessage.Timestamp
				}
			}

			// service window
			if time.Now().UTC().Sub((lastMessageTime).UTC()) < time.Hour*24 {
				whatsappWindow.ServiceWindowStartTime = &lastMessageTime
			}
			// 至少有一个才返回
			if whatsappWindow.ServiceWindowStartTime != nil {
				result.WhatsappConversationWindow = &whatsappWindow
			}
		}
	}
	// 获取消息
	messages, err := QueryCDSSMessages(*cr.Tenant, *cr.Platform, conversationId)
	if err != nil {
		return nil, cerror.New(fmt.Sprintf("failed to fetch messages by conversation %s", conversationId), http.StatusInternalServerError)
	}

	if messages == nil {
		result.Messages = []dto.CDSSMessage{}
	} else {
		result.Messages = messages
	}
	return result, nil
}

func TryToSyncMessages(ctx context.Context, cr dto.CtintR, conversationId string, messages []platformclientv2.Messagedetails, customerParticipant *platformclientv2.Messagemediaparticipant) error {
	if len(messages) == 0 {
		return nil
	}
	// 将messages 转成 SyncMessagesReq
	var payloadArray []dto.SyncMessagesItem
	for _, message := range messages {
		payloadItem := dto.SyncMessagesItem{
			MessageID:     message.MessageId,
			MessageStatus: message.MessageStatus,
			ParticipantID: customerParticipant.Id,
			UserID:        customerParticipant.FromAddress.AddressNormalized,
			UserName:      customerParticipant.FromAddress.Name,
		}
		payloadArray = append(payloadArray, payloadItem)
	}
	payload := dto.SyncMessagesPayload{
		Messages:       payloadArray,
		NeedUpdateOnly: true,
	}

	globalConfig := commonConfig.GlobalConfigV2[*cr.Tenant]
	host := *globalConfig.GetString("services.ctint-datasync-hub.host")
	basepath := *globalConfig.GetString("services.ctint-datasync-hub.basepath")
	url := host + basepath + fmt.Sprintf("/messages/conversations/%s/messages", conversationId)

	resp, err := cdssServiceUtils.IRequest(ctx, cr, http.MethodPut, url, payload, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return cerror.WrapResp(resp, "failed to sync messages")
	}
	return nil
}

func QueryCDSSMessages(tenant, platform, conversationId string) ([]dto.CDSSMessage, error) {
	dbMessages, err := dab.GetMessagesByConversationIds(tenant, platform, conversationId)
	if err != nil {
		return nil, err
	}
	var result []dto.CDSSMessage
	for _, dbm := range dbMessages {
		vm, err := dbm.VO()
		if err != nil {
			logger.Error(fmt.Sprintf("failed to convert message to vo %s", dbm.ID))
			continue
		}
		result = append(result, *vm)
	}
	return result, nil
}
func QueryLastMessageByPhone(tenant, platform, phone string) (*dabo.Message, error) {
	return dab.GetCDSSLastInboundMessageByPhone(tenant, platform, phone)
}

func QueryChannelByIntegrationId(tenant, platform, integrationId string) (*dabo.Channel, error) {
	return dab.FetchChannelByIntegrationId(tenant, platform, integrationId)
}

func QueryLastMessagesByConversations(tenant, platform string, conversationIds []string, direction string) ([]vo.CDSSConversationLastMessage, error) {
	if len(conversationIds) == 0 {
		return []vo.CDSSConversationLastMessage{}, nil
	}

	// Set default direction if empty
	if direction == "" {
		direction = "all"
	}

	// Get last messages from DAB layer with direction filter
	dbMessages, err := dab.GetLastMessagesByConversationIdsWithDirection(tenant, platform, direction, conversationIds...)
	if err != nil {
		return nil, err
	}

	// Convert DAB messages to DTO messages
	var cdssMessages []dto.CDSSMessage
	for _, dbMessage := range dbMessages {
		cdssMessage, err := dbMessage.VO()
		if err != nil {
			logger.Error(fmt.Sprintf("failed to convert message to VO for conversation %s: %v", *dbMessage.ConversationID, err))
			continue
		}
		cdssMessages = append(cdssMessages, *cdssMessage)
	}

	// Group messages by conversation and direction
	if direction == "both" {
		// For "both", we need to group by conversation and separate inbound/outbound
		conversationMap := make(map[string]*vo.CDSSConversationLastMessage)

		// Initialize map with all conversation IDs
		for _, convId := range conversationIds {
			conversationMap[convId] = &vo.CDSSConversationLastMessage{
				ConversationId: &convId,
			}
		}

		// Fill with actual messages
		for _, msg := range cdssMessages {
			if msg.ConversationID != nil {
				convMsg := conversationMap[*msg.ConversationID]
				if msg.Direction != nil {
					if *msg.Direction == "inbound" {
						convMsg.LastInboundMessage = &msg
					} else if *msg.Direction == "outbound" {
						convMsg.LastOutboundMessage = &msg
					}
				}
			}
		}

		// Convert map to slice maintaining order
		var result []vo.CDSSConversationLastMessage
		for _, convId := range conversationIds {
			if convMsg, exists := conversationMap[convId]; exists {
				result = append(result, *convMsg)
			}
		}

		return result, nil
	} else {
		// For "all", "inbound", "outbound", group by conversation with lastMessage
		conversationMap := make(map[string]*dto.CDSSMessage)

		// Fill with actual messages
		for _, msg := range cdssMessages {
			if msg.ConversationID != nil {
				conversationMap[*msg.ConversationID] = &msg
			}
		}

		// Convert to result maintaining input order
		var result []vo.CDSSConversationLastMessage
		for _, convId := range conversationIds {
			convMsg := vo.CDSSConversationLastMessage{
				ConversationId: &convId,
			}
			if msg, exists := conversationMap[convId]; exists {
				convMsg.LastMessage = msg
			}
			result = append(result, convMsg)
		}

		return result, nil
	}
}

// ProcessMessageConversationResult 处理消息对话结果，提取对话详情和最后的入站消息。
// 此函数主要用于解析和处理消息对话数据，以获取特定用户相关的对话详情和最后的入站消息。
// 参数:
//
//	conversation - 平台客户端的消息对话对象。
//	userId - 用户ID，用于识别特定的用户。
//
// 返回值:
//
//	*vo.CDSSFetchConversationDetailResp - 对话详情响应对象，包含对话ID、客户信息、当前参与者等。
//	*platformclientv2.Messagedetails - 最后的入站消息详情。
func ProcessMessageConversationResult(conversation platformclientv2.Messageconversation, userId string) (*vo.CDSSFetchConversationDetailResp, *platformclientv2.Messagemediaparticipant) {
	if conversation.Participants == nil {
		return nil, nil
	}
	var agentParticipant *platformclientv2.Messagemediaparticipant
	var agentDisconnectedParticipant *platformclientv2.Messagemediaparticipant
	var customerParticipant *platformclientv2.Messagemediaparticipant
	var activeConsultParticipant *platformclientv2.Messagemediaparticipant

	for _, participant := range *conversation.Participants {
		if participant.Purpose == nil {
			continue
		}
		switch constants.ParticipantType(*participant.Purpose) {
		case constants.ParticipantTypeAgent, constants.ParticipantTypeUser:
			if participant.User != nil && participant.User.Id != nil {
				// 尝试获取自己Id的 + 未end的 + 没有disconnect 的participant
				if *participant.User.Id == userId && participant.EndTime == nil && participant.DisconnectType == nil {
					agentParticipant = &participant
				} else if *participant.User.Id == userId && participant.State != nil && *participant.State == "disconnected" && participant.DisconnectType != nil {
					//如果全部都是disconnected , 有个保底,取时间最后的那个
					if agentDisconnectedParticipant == nil {
						agentDisconnectedParticipant = &participant
					} else if participant.EndTime != nil && participant.EndTime.After(*agentDisconnectedParticipant.EndTime) {
						agentDisconnectedParticipant = &participant
					}
				}
				// 尝试获取其他的活跃agent,则取未end的
				if *participant.User.Id != userId && participant.EndTime == nil && participant.DisconnectType == nil && participant.State != nil && *participant.State == MessageConversationStateAlerting {
					if activeConsultParticipant == nil {
						activeConsultParticipant = &participant
					} else if participant.EndTime != nil && participant.EndTime.After(*activeConsultParticipant.EndTime) {
						activeConsultParticipant = &participant
					}
				}
			}
		case constants.ParticipantTypeCustomer:
			if customerParticipant == nil {
				customerParticipant = &participant
			}
		}
	}
	if agentParticipant == nil {
		agentParticipant = agentDisconnectedParticipant
	}

	var integrationId *string
	var customerFrom *string
	var customerName *string
	var startTime *time.Time

	if customerParticipant != nil {
		if customerParticipant.ToAddress != nil && customerParticipant.ToAddress.AddressNormalized != nil {
			integrationId = customerParticipant.ToAddress.AddressNormalized
		}
		if customerParticipant.FromAddress != nil && customerParticipant.FromAddress.AddressNormalized != nil {
			customerFrom = customerParticipant.FromAddress.AddressNormalized
		}
		if customerParticipant.Name != nil {
			customerName = customerParticipant.Name
		}
		startTime = customerParticipant.StartTime

	}

	result := vo.CDSSFetchConversationDetailResp{
		ConversationId: conversation.Id,
		Customer: &vo.CDSSMessageConversationCustomerInfo{
			IntegrationId: integrationId,
			From:          customerFrom,
			Username:      customerName,
		},
		CurParticipant: agentParticipant,
		StartTime:      startTime,
	}
	if activeConsultParticipant != nil {
		result.ActiveConsultParticipant = activeConsultParticipant
	}
	return &result, customerParticipant
}
