package conversationService

import (
	"context"
	ecs "ctint-cdss-message/src/constants/errorCodes"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/models/vo"
	"ctint-cdss-message/src/services/gcService"
	"github.com/kuah/cerror"
)

// CreateConversation Create a new conversation
func CreateConversation(ctx context.Context, payload dto.CDSSMessageConversationCreationReq) (*vo.GCConversationCreationResp, error) {
	if payload.GCAuthorization == nil {
		return nil, cerror.New("GC Authorization token is empty", ecs.RequestErrorCodesGCTokenEmpty)
	}
	return gcService.CreateConversation(ctx, *payload.Tenant, *payload.GCAuthorization, dto.MessageConversationCreationPayload{
		ExternalContactId:       payload.ExternalContactId,
		QueueID:                 payload.QueueID,
		ToAddress:               payload.ToAddress,
		ToAddressMessengerType:  payload.ToAddressMessengerType,
		UseExistingConversation: payload.UseExistingConversation,
		UseUserFromAddress:      payload.UseUserFromAddress,
	})
}
