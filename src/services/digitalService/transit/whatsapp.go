package transit

//type WhatsappMessage struct {
//	MessagingProduct *string `json:"messaging_product"`
//	RecipientType    *string `json:"recipient_type"`
//	To               *string `json:"to"`
//	Type             *string `json:"type"`
//	Interactive      *struct {
//		Type   *string `json:"type"`
//		Header *struct {
//			Type  *string `json:"type"`
//			Text  *string `json:"text,omitempty"`
//			Image *struct {
//				ID   *string `json:"id,omitempty"`
//				Link *string `json:"link"`
//				Data *string `json:"data,omitempty"` //  cdss 独有
//			} `json:"image,omitempty"`
//			Video *struct {
//				ID *string `json:"id,omitempty"`
//			} `json:"video,omitempty"`
//			Document *struct {
//				ID *string `json:"id,omitempty"`
//			} `json:"document,omitempty"`
//		} `json:"header,omitempty"`
//		Body *struct {
//			Text *string `json:"text"`
//		} `json:"body,omitempty"`
//		Footer *struct {
//			Text *string `json:"text"`
//		} `json:"footer,omitempty"`
//		Action *struct {
//			Name       *string `json:"name,omitempty"`
//			Parameters *struct {
//				DisplayText *string `json:"display_text,omitempty"`
//				URL         *string `json:"url,omitempty"`
//			} `json:"parameters,omitempty"`
//			Sections *[]struct {
//				Title *string `json:"title"`
//				Rows  *[]struct {
//					ID          *string `json:"id"`
//					Title       *string `json:"title"`
//					Description *string `json:"description,omitempty"`
//				} `json:"rows"`
//			} `json:"sections,omitempty"`
//			Button  *string `json:"button,omitempty"`
//			Buttons *[]struct {
//				Type  *string `json:"type"`
//				Reply *struct {
//					ID    *string `json:"id"`
//					Title *string `json:"title"`
//				} `json:"reply"`
//			} `json:"buttons,omitempty"`
//		} `json:"action"`
//	} `json:"interactive"`
//	// 模版消息
//	Template *WhatsappTemplate `json:"template,omitempty"`
//}

type WhatsappTemplate struct {
	Name       *string                  `json:"name"`
	Language   WhatsappTemplateLanguage `json:"language"`
	Components []Component              `json:"components,omitempty"`
}
type WhatsappTemplateLanguage struct {
	Code string `json:"code"`
}

// 组件类型
type ComponentType string

const (
	ComponentHeader ComponentType = "header"
	ComponentBody   ComponentType = "body"
	ComponentButton ComponentType = "button"
)

// 按钮类型
type ButtonType string

const (
	QuickReplyButton ButtonType = "quick_reply"
	URLButton        ButtonType = "url"
)

// 参数类型
type ParameterType string

const (
	TextParameter     ParameterType = "text"
	CurrencyParameter ParameterType = "currency"
	DateTimeParameter ParameterType = "date_time"
)

// 货币参数
type Currency struct {
	FallbackValue string `json:"fallback_value"`
	Code          string `json:"currency_code"`
	Amount        int64  `json:"amount_1000"`
}

// 组件定义
type Component struct {
	Type       ComponentType `json:"type"`                 // 必需
	SubType    *string       `json:"sub_type,omitempty"`   // 仅按钮需要（指针判断存在性）
	Parameters []Parameter   `json:"parameters,omitempty"` // 动态参数
	Index      *int          `json:"index,omitempty"`      // 使用指针区分 0 和未设置
	Text       *string       `json:"text,omitempty"`       // 可能动态生成
	Buttons    []Button      `json:"buttons,omitempty"`    // 按钮数组
}

// 参数定义（使用严格指针模式）
type Parameter struct {
	Type     ParameterType `json:"type"`
	Text     *string       `json:"text,omitempty"`      // 文本类型时必填
	Currency *Currency     `json:"currency,omitempty"`  // 货币类型时必填
	DateTime *string       `json:"date_time,omitempty"` // 日期类型时必填
	Image    *struct {
		Link *string `json:"link,omitempty"`
	} `json:"image,omitempty"`
	Document *struct {
		Link *string `json:"link,omitempty"`
	} `json:"document,omitempty"`
	Video *struct {
		Link *string `json:"link,omitempty"`
	} `json:"video,omitempty"`
	Action *Action `json:"action,omitempty"`
}

type Action struct {
	FlowActionData map[string]interface{} `json:"flow_action_data"`
	FlowToken      *string                `json:"flow_token,omitempty"`
}

//// 按钮定义（URL 和 Payload 互斥）
//type Button struct {
//	Type    ButtonType `json:"type"`
//	Text    string     `json:"text"`
//	Payload *string    `json:"payload,omitempty"` // quick_reply 时必填
//	URL     *string    `json:"url,omitempty"`     // url 类型时必填
//}

// Interactive 表示消息中的交互式组件
type InteractiveTemplate struct {
	Type   *string            `json:"type"`
	Header *InteractiveHeader `json:"header,omitempty"`
	Body   *InteractiveBody   `json:"body,omitempty"`
	Footer *InteractiveFooter `json:"footer,omitempty"`
	Action *InteractiveAction `json:"action"`
}

// InteractiveHeader 表示交互式组件的头部信息
type InteractiveHeader struct {
	Type     *string              `json:"type"`
	Text     *string              `json:"text,omitempty"`
	Image    *InteractiveImage    `json:"image,omitempty"`
	Video    *InteractiveVideo    `json:"video,omitempty"`
	Document *InteractiveDocument `json:"document,omitempty"`
}

// InteractiveImage 表示头部中的图片信息
type InteractiveImage struct {
	ID   *string `json:"id,omitempty"`
	Link *string `json:"link"`
	Data *string `json:"data,omitempty"` // cdss 独有
}

// InteractiveVideo 表示头部中的视频信息
type InteractiveVideo struct {
	ID *string `json:"id,omitempty"`
}

// InteractiveDocument 表示头部中的文档信息
type InteractiveDocument struct {
	ID *string `json:"id,omitempty"`
}

// InteractiveBody 表示交互式组件的主体文本
type InteractiveBody struct {
	Text *string `json:"text"`
}

// InteractiveFooter 表示交互式组件的页脚文本
type InteractiveFooter struct {
	Text *string `json:"text"`
}

// InteractiveAction 表示交互式组件的操作区域
type InteractiveAction struct {
	Name       *string                      `json:"name,omitempty"`
	Parameters *InteractiveActionParameters `json:"parameters,omitempty"`
	Sections   *[]InteractiveActionSection  `json:"sections,omitempty"`
	Button     *string                      `json:"button,omitempty"`
	Buttons    *[]InteractiveActionButton   `json:"buttons,omitempty"`
}

// InteractiveActionParameters 表示操作区域的参数
type InteractiveActionParameters struct {
	DisplayText *string `json:"display_text,omitempty"`
	URL         *string `json:"url,omitempty"`
	InteractiveActionParametersFlow
}
type InteractiveActionParametersFlow struct {
	FlowMessageVersion *string                       `json:"flow_message_version,omitempty"`
	FlowToken          *string                       `json:"flow_token,omitempty"`
	FlowID             *string                       `json:"flow_id,omitempty"`
	FlowCTA            *string                       `json:"flow_cta,omitempty"`
	FlowAction         *string                       `json:"flow_action,omitempty"`
	FlowActionPayload  *InteractiveFlowActionPayload `json:"flow_action_payload,omitempty"`
}

type InteractiveFlowActionPayload struct {
	Screen *string `json:"screen,omitempty"`
}

// InteractiveActionSection 表示操作区域中的一个部分
type InteractiveActionSection struct {
	Title *string                 `json:"title"`
	Rows  *[]InteractiveActionRow `json:"rows"`
}

// InteractiveActionRow 表示部分中的一行数据
type InteractiveActionRow struct {
	ID          *string `json:"id"`
	Title       *string `json:"title"`
	Description *string `json:"description,omitempty"`
}

// InteractiveActionButton 表示操作区域中的按钮
type InteractiveActionButton struct {
	Type  *string                       `json:"type"`
	Reply *InteractiveActionButtonReply `json:"reply"`
}

// InteractiveActionButtonReply 表示按钮的回复信息
type InteractiveActionButtonReply struct {
	ID    *string `json:"id"`
	Title *string `json:"title"`
}
