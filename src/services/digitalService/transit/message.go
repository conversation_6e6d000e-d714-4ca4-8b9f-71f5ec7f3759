package transit

import (
	"context"
	utils "ctint-cdss-message/src/pkg"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/mypurecloud/platform-client-sdk-go/v129/platformclientv2"
	"github.com/pkg/errors"
	"net/url"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type DigitalMessageMode string

const (
	DigitalMessageModeDirect DigitalMessageMode = "direct"
	DigitalMessageModeMQ     DigitalMessageMode = "mq"
)

type Message struct {
	dto.CtintR                  `cv:"true"`
	Mode                        DigitalMessageMode   `json:"mode,omitempty"`
	From                        string               `json:"from"`
	To                          *To                  `json:"to,omitempty"`
	FromName                    string               `json:"fromName"`
	ID                          string               `json:"id"`
	Timestamp                   string               `json:"timestamp"`
	Type                        string               `json:"type"`
	ParentId                    *string              `json:"parentId"`
	Text                        *Text                `json:"text,omitempty"`
	Image                       *Media               `json:"image,omitempty"`
	Video                       *Media               `json:"video,omitempty"`
	Audio                       *Media               `json:"audio,omitempty"`
	Document                    *Media               `json:"document,omitempty"`
	Sticker                     *Media               `json:"sticker,omitempty"`
	Context                     *Context             `json:"context,omitempty"`
	Interactive                 *Interactive         `json:"interactive,omitempty"`
	Button                      *Button              `json:"button,omitempty"`
	WhatsappTemplate            *WhatsappTemplate    `json:"template,omitempty"`
	WhatsappInteractiveTemplate *InteractiveTemplate `json:"interactiveTemplate,omitempty"`
	AccessChannel
	ServicesConfig
	PlatformMetaData  *[]byte `json:"platformMetaData"`
	ByPassGateway     bool    `json:"byPassGateway"`
	OriginatingEntity *string `json:"originatingEntity,omitempty"`
}

type To struct {
	Platform string `json:"platform"`
	Address  string `json:"address"`
}
type AccessChannel struct {
	AccessPlatform     *string `json:"message:platform,omitempty"`        // 消息平台
	EntryId            *string `json:"message:receiver_owner,omitempty"`  // 消息主体
	DisplayPhoneNumber *string `json:"message:receiver_number,omitempty"` // 接入电话号码
	PhoneNumberID      *string `json:"message:phone,omitempty"`           // 接入电话号码的id
	SenderID           *string `json:"message:senderId,omitempty"`        // 发送者id
}

type ServicesConfig struct {
	VirusScan int `json:"virusScan,omitempty"`
	Stt       int `json:"stt,omitempty"`
	Pii       int `json:"pii,omitempty"`
	Saa       int `json:"saa,omitempty"`
}

type Media struct {
	MimeType string `json:"mime_type"`
	SHA256   string `json:"sha256"`
	ID       string `json:"id"`
	Caption  string `json:"caption,omitempty"`
	FileName string `json:"fileName,omitempty"`
	Url      string `json:"url"`
}
type Document struct {
	MimeType string `json:"mime_type"`
	SHA256   string `json:"sha256"`
	ID       string `json:"id"`
	Caption  string `json:"caption,omitempty"`
	FileName string `json:"fileName,omitempty"`
	Url      string `json:"url"`
}
type Sticker struct {
	MimeType string `json:"mime_type"`
	SHA256   string `json:"sha256"`
	ID       string `json:"id"`
	Caption  string `json:"caption,omitempty"`
	FileName string `json:"fileName,omitempty"`
	Url      string `json:"url"`
}

type Context struct {
	From string `json:"from"`
	ID   string `json:"id"`
}

type Text struct {
	Body string `json:"body"`
}

// Interactive 表示交互部分的结构体
type Interactive struct {
	ButtonReply *ButtonReply `json:"button_reply,omitempty"`
	ListReply   *ListReply   `json:"list_reply,omitempty"`
	NFMReply    *NFMReply    `json:"nfm_reply,omitempty"`
	Type        string       `json:"type"`
}
type NFMReply struct {
	ResponseJSON string `json:"response_json"`
	Body         string `json:"body"`
	Name         string `json:"name"`
}

func (n NFMReply) Response() *NFMResponse {
	if n.ResponseJSON != "" {
		var result NFMResponse
		err := json.Unmarshal([]byte(n.ResponseJSON), &result)
		if err != nil {
			fmt.Printf("Error parsing NFMReply: %v", err)
			return nil
		} else {
			return &result
		}
	}
	return nil
}

type NFMResponseMap map[string]interface{}

func (n NFMReply) ResponseToMap() *NFMResponseMap {
	if n.ResponseJSON != "" {
		var result NFMResponseMap
		err := json.Unmarshal([]byte(n.ResponseJSON), &result)
		if err != nil {
			fmt.Printf("Error parsing NFMReply to Map: %v", err)
			return nil
		} else {
			return &result
		}
	}
	return nil
}
func (nm NFMResponseMap) Value(key string) interface{} {
	if value, ok := nm[key]; ok {
		return value
	}
	return nil
}
func (nm NFMResponseMap) ValueCompare(key string, target interface{}) bool {
	if value, ok := nm[key]; ok {
		return value == target
	}
	return false
}

// ButtonReply 表示按钮回复部分的结构体
type ButtonReply struct {
	ID    string `json:"id"`
	Title string `json:"title"`
}
type Button struct {
	Payload string `json:"payload"`
	Text    string `json:"text"`
}

// ListReply 表示列表回复部分的结构体
type ListReply struct {
	Description string `json:"description"`
	ID          string `json:"id"`
	Title       string `json:"title"`
}

// NFMResponse
/* custom response for nfm */
type NFMResponse struct {
	FlowToken string `json:"flow_token,omitempty"`
	NFMBusinessDiversionResponse
}

type NFMBusinessDiversionResponse struct {
	ContactAgent string `json:"contact_agent,omitempty"`
}

func (message *Message) RawRequest() *string {
	fullReq, err := json.Marshal(message)
	if err != nil {
		return nil
	}
	return utils.Ptr(string(fullReq))
}

func (message *Message) IsFromProvider(provider constants.MessageProvider) bool {
	return utils.Value(message.AccessChannel.AccessPlatform) == string(provider)
}
func (message *Message) IsTargetedProvider(provider constants.MessageProvider) bool {
	if message.To != nil {
		platform := (*message.To).Platform
		return platform == string(provider)
	}
	return false
}

//
//func (message *Message) ApplyServicesConfig(channel *config.Channel) *Message {
//	message.VirusScan = int(message.ShouldProcessVirusScan(channel))
//	message.Stt = int(message.ShouldProcessStt(channel))
//	message.Pii = int(message.ShouldProcessPii(channel))
//	message.Saa = int(message.ShouldProcessSaa(channel))
//	return message
//}
//
//func (message *Message) BaseMetaData() models.CDSSInboundMessageMeta {
//	embedMetaData := models.CDSSInboundMessageMeta{
//		BaseMeta: models.BaseMeta{
//			Platform:           message.AccessPlatform,
//			DisplayPhoneNumber: message.DisplayPhoneNumber,
//			PhoneNumberID:      message.PhoneNumberID,
//			MessageID:          utils.Ptr(message.ID),
//			SenderID:           message.SenderID,
//		},
//		MessageCategory: constants.CDSSMessageCategoryNormal,
//	}
//	if message.Context != nil {
//		embedMetaData.ContextMeta = models.ContextMeta{
//			ContextId:   &message.Context.ID,
//			ContextFrom: &message.Context.From,
//		}
//	}
//	if message.PlatformMetaData != nil {
//		embedMetaData.PlatformMetaData = utils.Ptr(string(*message.PlatformMetaData))
//
//		// hardcode for qhms
//		if message.PlatformMetaData != nil {
//
//		}
//		m := map[string]string{}
//		err := json.Unmarshal(*message.PlatformMetaData, &m)
//		if err == nil {
//			if embedMetaData.AttachedMapping == nil {
//				embedMetaData.AttachedMapping = map[string]string{}
//			}
//			if q_pqn, ok := m["service"]; ok {
//				embedMetaData.AttachedMapping["Para_QueueName"] = q_pqn
//			}
//			if q_pql, ok := m["language"]; ok {
//				embedMetaData.AttachedMapping["Para_Language"] = q_pql
//			}
//		}
//
//	}
//	return embedMetaData
//}
//
//func (message *Message) ConvertToGC(spMeta *models.CDSSInboundMessageMeta) (platformclientv2.Openinboundnormalizedmessage, error) {
//	var payload *platformclientv2.Openinboundnormalizedmessage
//
//	// text & attachment
//	textBody, attachmentPayload := message.ConvertTextNAttachment()
//
//	// meta data
//	var embedMetaData models.CDSSInboundMessageMeta
//	if spMeta == nil {
//		// preset meta
//		embedMetaData = message.BaseMetaData()
//		embedMetaData.VirusScanMeta = models.VirusScanMeta{
//			VirusScan: utils.Ptr(fmt.Sprintf("%d", message.VirusScan)),
//		}
//		embedMetaData.SttMeta = models.SttMeta{
//			Stt: utils.Ptr(fmt.Sprintf("%d", message.Stt)),
//		}
//		embedMetaData.PiiMeta = models.PiiMeta{
//			Pii: utils.Ptr(fmt.Sprintf("%d", message.Pii)),
//		}
//		embedMetaData.SaaMeta = models.SaaMeta{
//			Saa: utils.Ptr(fmt.Sprintf("%d", message.Saa)),
//		}
//	} else {
//		embedMetaData = *spMeta
//		if cttool.Stringer(embedMetaData.MessageCategory).OneOf(constants.CDSSMessageCategoryVirusScanPlaceholder, constants.CDSSMessageCategoryPiiPlaceholder) {
//			// preset meta
//			embedMetaData.VirusScanMeta = models.VirusScanMeta{
//				VirusScan: utils.Ptr(fmt.Sprintf("%d", message.VirusScan)),
//			}
//			embedMetaData.SttMeta = models.SttMeta{
//				Stt: utils.Ptr(fmt.Sprintf("%d", message.Stt)),
//			}
//			embedMetaData.PiiMeta = models.PiiMeta{
//				Pii: utils.Ptr(fmt.Sprintf("%d", message.Pii)),
//			}
//			embedMetaData.SaaMeta = models.SaaMeta{
//				Saa: utils.Ptr(fmt.Sprintf("%d", message.Saa)),
//			}
//		}
//	}
//
//	switch message.Type {
//	case constants.WhatsAppMessageTypeText:
//		payload = &platformclientv2.Openinboundnormalizedmessage{
//			Channel: &platformclientv2.Openinboundmessagemessagingchannel{
//				From: &platformclientv2.Openmessagingfromrecipient{
//					Nickname:  &message.FromName,
//					Id:        &message.From,
//					IdType:    utils.Ptr("Phone"),
//					FirstName: &message.FromName,
//				},
//				Time:      utils.Ptr(waTimeToGCTime(message.Timestamp)),
//				MessageId: &message.ID,
//			},
//			Text: &message.Text.Body,
//		}
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	case constants.WhatsAppMessageTypeImage, constants.WhatsAppMessageTypeVideo, constants.WhatsAppMessageTypeAudio, constants.WhatsAppMessageTypeDocument, constants.WhatsAppMessageTypeSticker:
//		payload = &platformclientv2.Openinboundnormalizedmessage{
//			Channel: &platformclientv2.Openinboundmessagemessagingchannel{
//				From: &platformclientv2.Openmessagingfromrecipient{
//					Nickname:  &message.FromName,
//					Id:        &message.From,
//					IdType:    utils.Ptr("Phone"),
//					FirstName: &message.FromName,
//				},
//				Time:      utils.Ptr(waTimeToGCTime(message.Timestamp)),
//				MessageId: &message.ID,
//			},
//			Text: &textBody,
//			Content: &[]platformclientv2.Openinboundmessagecontent{
//				{
//					Attachment: &attachmentPayload,
//				},
//			},
//		}
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	case constants.WhatsAppMessageTypeInteractive:
//		switch message.Interactive.Type {
//		case constants.InteractiveReplyTypeList:
//			displayText := message.Interactive.ListReply.Title + "\n" + message.Interactive.ListReply.Description
//			embedMetaData.InteractiveType = &message.Interactive.Type
//			embedMetaData.InteractiveId = &message.Interactive.ListReply.ID
//			payload = &platformclientv2.Openinboundnormalizedmessage{
//				Channel: &platformclientv2.Openinboundmessagemessagingchannel{
//					From: &platformclientv2.Openmessagingfromrecipient{
//						Nickname:  &message.FromName,
//						Id:        &message.From,
//						IdType:    utils.Ptr("Phone"),
//						FirstName: &message.FromName,
//					},
//					Time:      utils.Ptr(waTimeToGCTime(message.Timestamp)),
//					MessageId: &message.ID,
//				},
//				Text: &displayText,
//			}
//			if md := embedMetaData.Data(); len(md) > 0 {
//				payload.Metadata = &md
//				channelAttr := embedMetaData.ChannelData()
//				channelMeta := map[string]interface{}{
//					"customAttributes": channelAttr,
//				}
//				payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//			}
//		case constants.InteractiveReplyTypeButton:
//			displayText := message.Interactive.ButtonReply.Title
//			embedMetaData.InteractiveType = &message.Interactive.Type
//			embedMetaData.InteractiveId = &message.Interactive.ButtonReply.ID
//			payload = &platformclientv2.Openinboundnormalizedmessage{
//				Channel: &platformclientv2.Openinboundmessagemessagingchannel{
//					From: &platformclientv2.Openmessagingfromrecipient{
//						Nickname:  &message.FromName,
//						Id:        &message.From,
//						IdType:    utils.Ptr("Phone"),
//						FirstName: &message.FromName,
//					},
//					Time:      utils.Ptr(waTimeToGCTime(message.Timestamp)),
//					MessageId: &message.ID,
//				},
//				Text: &displayText,
//			}
//			if md := embedMetaData.Data(); len(md) > 0 {
//				payload.Metadata = &md
//				channelAttr := embedMetaData.ChannelData()
//				channelMeta := map[string]interface{}{
//					"customAttributes": channelAttr,
//				}
//				payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//			}
//		default:
//			// 这里也包含了whatsapp flow的结果
//			return platformclientv2.Openinboundnormalizedmessage{}, errors.New(fmt.Sprintf("unsupported type for message type : %s", message.Type))
//		}
//	case constants.WhatsAppMessageTypeButton:
//		displayText := message.Button.Text
//		embedMetaData.InteractiveType = &message.Type
//		embedMetaData.InteractiveButtonPayload = &message.Button.Payload
//		payload = &platformclientv2.Openinboundnormalizedmessage{
//			Channel: &platformclientv2.Openinboundmessagemessagingchannel{
//				From: &platformclientv2.Openmessagingfromrecipient{
//					Nickname:  &message.FromName,
//					Id:        &message.From,
//					IdType:    utils.Ptr("Phone"),
//					FirstName: &message.FromName,
//				},
//				Time:      utils.Ptr(waTimeToGCTime(message.Timestamp)),
//				MessageId: &message.ID,
//			},
//			Text: &displayText,
//		}
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	}
//
//	// apply service
//	if embedMetaData.MessageCategory == constants.CDSSMessageCategoryVirusScanPlaceholder {
//		// 将attachment info, 放在meta data 中
//		attachmentInfoJsonData, err := json.Marshal(attachmentPayload)
//		if err != nil {
//			fmt.Println("Error marshaling JSON:", err)
//			return platformclientv2.Openinboundnormalizedmessage{}, err
//		}
//		jsonStr := string(attachmentInfoJsonData)
//		embedMetaData.VirusScanMeta.VirusScanAttachmentInfo = &jsonStr
//		// gc display text
//		textBody = textBody + "[[virus scanning :" + *attachmentPayload.Filename + "]]"
//		payload.Content = nil
//		payload.Text = utils.Ptr(textBody)
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	}
//	if embedMetaData.MessageCategory == constants.CDSSMessageCategoryPiiPlaceholder {
//		attachmentInfoJsonData, err := json.Marshal(attachmentPayload)
//		if err != nil {
//			fmt.Println("Error marshaling JSON:", err)
//			return platformclientv2.Openinboundnormalizedmessage{}, err
//		}
//		// 将字节切片转换为字符串并输出
//		jsonStr := string(attachmentInfoJsonData)
//		embedMetaData.PiiAttachmentInfo = &jsonStr
//		textBody = textBody + "[[Personally Identifiable Information message protecting :" + *attachmentPayload.Filename + "]]"
//		payload.Content = nil
//		payload.Text = utils.Ptr(textBody)
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	}
//	// 如果virus result, 遇到pii 要继续跑, 则先不上台真实的image
//	if embedMetaData.MessageCategory == constants.CDSSMessageCategoryVirusScanResult && message.Pii == int(constants.MessagePiiOptionOK) {
//		// 将attachment info, 放在meta data 中
//		attachmentInfoJsonData, err := json.Marshal(attachmentPayload)
//		if err != nil {
//			fmt.Println("Error marshaling JSON:", err)
//			return platformclientv2.Openinboundnormalizedmessage{}, err
//		}
//		jsonStr := string(attachmentInfoJsonData)
//		embedMetaData.VirusScanMeta.VirusScanAttachmentInfo = &jsonStr
//		// gc display text
//		textBody = textBody + "[[virus scanning :" + *attachmentPayload.Filename + "]]"
//		payload.Content = nil
//		payload.Text = utils.Ptr(textBody)
//		message.Type = "text"
//		if md := embedMetaData.Data(); len(md) > 0 {
//			payload.Metadata = &md
//			channelAttr := embedMetaData.ChannelData()
//			channelMeta := map[string]interface{}{
//				"customAttributes": channelAttr,
//			}
//			payload.Channel.Metadata = utils.Ptr(interface{}(channelMeta))
//		}
//	}
//	if payload != nil {
//		return *payload, nil
//	} else {
//		return platformclientv2.Openinboundnormalizedmessage{}, errors.New(fmt.Sprintf("unsupported type for message type : %s", message.Type))
//	}
//}
//func waTimeToGCTime(waTime string) time.Time {
//	// time format
//	timestamp, _ := strconv.ParseInt(waTime, 10, 64)
//	// 将 Unix 时间戳转换成 time.Time 对象
//	t := time.Unix(timestamp, 0)
//	// 转换为 UTC 并格式化为 ISO 8601 格式
//	return t.UTC()
//}
//
//func (message *Message) Media() *Media {
//	switch message.Type {
//	case constants.WhatsAppMessageTypeImage:
//		return message.Image
//	case constants.WhatsAppMessageTypeSticker:
//		return message.Sticker
//	case constants.WhatsAppMessageTypeVideo:
//		return message.Video
//	case constants.WhatsAppMessageTypeAudio:
//		return message.Audio
//	case constants.WhatsAppMessageTypeDocument:
//		return message.Document
//	}
//	return nil
//}
//
//func (message *Message) MediaLink() *string {
//	var link string
//	if message.Media() != nil && message.Media().Url != "" {
//		return &message.Media().Url
//	}
//	switch message.Type {
//	case constants.WhatsAppMessageTypeImage:
//		link = message.Image.Url
//		if message.Image.Url == "" {
//			//从external获取360的附件流
//			link = nacos.ProjectConfig.MediaProxy.Host + nacos.ProjectConfig.MediaProxy.BasePath + "/" + *message.DisplayPhoneNumber + "/" + message.Image.ID
//		}
//	case constants.WhatsAppMessageTypeSticker:
//		link = message.Sticker.Url
//		if message.Sticker.Url == "" {
//			link = nacos.ProjectConfig.MediaProxy.Host + nacos.ProjectConfig.MediaProxy.BasePath + "/" + *message.DisplayPhoneNumber + "/" + message.Sticker.ID
//		}
//	case constants.WhatsAppMessageTypeVideo:
//		link = message.Video.Url
//		if message.Video.Url == "" {
//			link = nacos.ProjectConfig.MediaProxy.Host + nacos.ProjectConfig.MediaProxy.BasePath + "/" + *message.DisplayPhoneNumber + "/" + message.Video.ID
//		}
//	case constants.WhatsAppMessageTypeAudio:
//		link = message.Audio.Url
//		if message.Audio.Url == "" {
//			link = nacos.ProjectConfig.MediaProxy.Host + nacos.ProjectConfig.MediaProxy.BasePath + "/" + *message.DisplayPhoneNumber + "/" + message.Audio.ID
//		}
//	case constants.WhatsAppMessageTypeDocument:
//		link = message.Document.Url
//		if message.Document.Url == "" {
//			link = nacos.ProjectConfig.MediaProxy.Host + nacos.ProjectConfig.MediaProxy.BasePath + "/" + *message.DisplayPhoneNumber + "/" + message.Document.ID
//		}
//	}
//
//	return &link
//}
//func (message *Message) TextContent() string {
//	switch message.Type {
//	case constants.WhatsAppMessageTypeImage:
//		return message.Image.Caption
//	case constants.WhatsAppMessageTypeSticker:
//		return message.Sticker.Caption
//	case constants.WhatsAppMessageTypeVideo:
//		return message.Video.Caption
//	case constants.WhatsAppMessageTypeAudio:
//		return message.Audio.Caption
//	case constants.WhatsAppMessageTypeDocument:
//		return message.Document.Caption
//	case constants.WhatsAppMessageTypeText:
//		return message.Text.Body
//	default:
//		return ""
//	}
//}
//
//// GetCaption 根据消息类型提取 caption，包含非空判断
//func (message *Message) GetCaption() string {
//	if message == nil {
//		return ""
//	}
//
//	switch message.Type {
//	case constants.WhatsAppMessageTypeText:
//		if message.Text != nil {
//			return message.Text.Body
//		}
//	case constants.WhatsAppMessageTypeImage:
//		if message.Image != nil {
//			return message.Image.Caption
//		}
//	case constants.WhatsAppMessageTypeVideo:
//		if message.Video != nil {
//			return message.Video.Caption
//		}
//	case constants.WhatsAppMessageTypeAudio:
//		if message.Audio != nil {
//			return message.Audio.Caption
//		}
//	case constants.WhatsAppMessageTypeDocument:
//		if message.Document != nil {
//			return message.Document.Caption
//		}
//	case constants.WhatsAppMessageTypeSticker:
//		if message.Sticker != nil {
//			return message.Sticker.Caption
//		}
//	}
//
//	return ""
//}
//func (message *Message) ConvertTextNAttachment() (string, platformclientv2.Opencontentattachment) {
//	var attachmentPayload platformclientv2.Opencontentattachment
//	var textBody string
//	link := message.MediaLink()
//	switch message.Type {
//	case constants.WhatsAppMessageTypeImage:
//		messageType := constants.GCMediaTypeImage
//		parts := strings.Split(message.Image.MimeType, "/")
//		fileType := parts[1]
//		fileName := message.Image.FileName
//		if message.Image.FileName == "" {
//			fileName = message.Image.ID + "." + fileType
//		}
//		if link != nil && *link != "" {
//			attachmentPayload = platformclientv2.Opencontentattachment{
//				Id:        utils.Ptr(message.Image.ID + "." + message.ID),
//				MediaType: &messageType,
//				Url:       link,
//				Mime:      &message.Image.MimeType,
//				Filename:  &fileName,
//				Sha256:    &message.Image.SHA256,
//			}
//		}
//		textBody = message.Image.Caption
//	case constants.WhatsAppMessageTypeSticker:
//		if link != nil && *link != "" {
//			messageType := constants.GCMediaTypeImage
//			parts := strings.Split(message.Sticker.MimeType, "/")
//			fileType := parts[1]
//			fileName := message.Sticker.FileName
//			if message.Sticker.FileName == "" {
//				fileName = message.Sticker.ID + "." + fileType
//			}
//			attachmentPayload = platformclientv2.Opencontentattachment{
//				Id:        utils.Ptr(message.Sticker.ID + "." + message.ID),
//				MediaType: &messageType,
//				Url:       link,
//				Mime:      &message.Sticker.MimeType,
//				Filename:  &fileName,
//				Sha256:    &message.Sticker.SHA256,
//			}
//		}
//		textBody = message.Sticker.Caption
//	case constants.WhatsAppMessageTypeVideo:
//		if link != nil && *link != "" {
//			messageType := constants.GCMediaTypeVideo
//			fileName := ""
//			if message.Video.FileName == "" {
//				//video 需要拼接一下文件名,不然在GC上download 没有扩展名
//				parts := strings.Split(message.Video.MimeType, "/")
//				fileType := parts[1]
//				fileName = message.Video.ID + "." + fileType
//			} else {
//				fileName = message.Video.FileName
//			}
//			attachmentPayload = platformclientv2.Opencontentattachment{
//				Id:        utils.Ptr(message.Video.ID + "." + message.ID),
//				MediaType: &messageType,
//				Url:       link,
//				Mime:      &message.Video.MimeType,
//				Filename:  &fileName,
//				Sha256:    &message.Video.SHA256,
//			}
//		}
//		textBody = message.Video.Caption
//	case constants.WhatsAppMessageTypeAudio:
//		if link != nil && *link != "" {
//			messageType := constants.GCMediaTypeAudio
//			fileName := ""
//			if message.Audio.FileName == "" {
//				fileExt := getFileExtensionFromMime(message.Audio.MimeType)
//				if fileExt == "" {
//					fileName = "AUDIO_" + message.Audio.ID
//				} else {
//					fileName = "AUDIO_" + message.Audio.ID + "." + fileExt
//				}
//			} else {
//				fileName = message.Audio.FileName
//			}
//			attachmentPayload = platformclientv2.Opencontentattachment{
//				Id:        utils.Ptr(message.Audio.ID + "." + message.ID),
//				MediaType: &messageType,
//				Url:       link,
//				Mime:      &message.Audio.MimeType,
//				Filename:  &fileName,
//				Sha256:    &message.Audio.SHA256,
//			}
//		}
//		textBody = message.Audio.Caption
//	case constants.WhatsAppMessageTypeDocument:
//		if link != nil && *link != "" {
//			messageType := constants.GCMediaTypeFile
//			fileName := ""
//			if message.Document.FileName == "" {
//				fileName = message.Document.ID
//			} else {
//				fileName = message.Document.FileName
//			}
//			attachmentPayload = platformclientv2.Opencontentattachment{
//				Id:        utils.Ptr(message.Document.ID + "." + message.ID),
//				MediaType: &messageType,
//				Url:       link,
//				Mime:      &message.Document.MimeType,
//				Filename:  &fileName,
//				Sha256:    &message.Document.SHA256,
//			}
//		}
//		textBody = message.Document.Caption
//	}
//	return textBody, attachmentPayload
//}
//
//func (message *Message) ShouldProcessSaa(channel *config.Channel) constants.MessageSaaOption {
//	// 检查 virus scan 服务是否启用
//	if nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.SAA.Switch != "open" {
//		return constants.MessageSaaOptionServiceDisable
//	}
//	// 检查 tenant 是否开启 virus scan
//	if channel.ServiceEnable.SAA != "open" {
//		return constants.MessageSaaOptionServiceTenantNoPermission
//	}
//
//	if message.Type == constants.WhatsAppMessageTypeText {
//		return constants.MessageSaaOptionOK
//	} else {
//		return constants.MessageSaaOptionMessageTypeNotSupported
//	}
//}
//
//func (message *Message) ShouldProcessVirusScan(channel *config.Channel) constants.MessageVirusScanOption {
//	//return constants.MessageVirusScanOptionServiceDisable
//	// 检查 virus scan 服务是否启用
//	if nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.VirusScan.Switch != "open" {
//		return constants.MessageVirusScanOptionServiceDisable
//	}
//	// 检查 tenant 是否开启 virus scan
//	if channel.ServiceEnable.VirusScan != "open" {
//		return constants.MessageVirusScanOptionServiceTenantNoPermission
//	}
//
//	if strings.HasPrefix(message.ID, constants.MessageIdVirusScanPrefix) {
//		return constants.MessageVirusScanOptionDone
//	}
//
//	// type condition
//	switch message.Type {
//	case constants.WhatsAppMessageTypeAudio, constants.WhatsAppMessageTypeVideo, constants.WhatsAppMessageTypeImage, constants.WhatsAppMessageTypeDocument, constants.WhatsAppMessageTypeSticker:
//		// 检查文件类型是否支持
//		supportedType := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.VirusScan.SupportedFileExt
//		if supportedType == "all" {
//			return constants.MessageVirusScanOptionOK
//		}
//		if message.Audio.FileName == "" {
//			// 获取拓展名
//			mExt := getFileExtensionFromMime(message.Audio.MimeType)
//			if mExt != "" && strings.Contains(supportedType, mExt) {
//				return constants.MessageVirusScanOptionOK
//			}
//		}
//		return constants.MessageVirusScanOptionFileNotSupported
//	default:
//		return constants.MessageVirusScanOptionMessageTypeNotSupported
//	}
//}
//func (message *Message) ShouldProcessStt(channel *config.Channel) constants.MessageSttOption {
//
//	// 检查 STT 服务是否启用
//	if nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.STT.Switch != "open" {
//		return constants.MessageSttOptionServiceDisable
//	}
//	// 检查 tenant 是否开启 STT
//	if channel.ServiceEnable.STT != "open" {
//		return constants.MessageSttOptionServiceTenantNoPermission
//	}
//	// 检查文件类型是否支持
//	supportedType := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.STT.SupportedFileExt
//	// type condition
//	switch message.Type {
//	case constants.WhatsAppMessageTypeAudio:
//		if message.Audio.FileName == "" {
//			// 获取拓展名
//			mExt := getFileExtensionFromMime(message.Audio.MimeType)
//			if mExt != "" && strings.Contains(supportedType, mExt) {
//				return constants.MessageSttOptionOK
//			}
//		}
//		return constants.MessageSttOptionFileNotSupported
//	case constants.WhatsAppMessageTypeDocument:
//		if message.Document.FileName != "" {
//			// 获取拓展名
//			ext := getFileExtensionFromName(message.Document.FileName)
//			if ext != nil && strings.Contains(supportedType, *ext) {
//				return constants.MessageSttOptionOK
//			}
//		}
//		return constants.MessageSttOptionFileNotSupported
//	default:
//		return constants.MessageSttOptionMessageTypeNotSupported
//	}
//}
//func (message *Message) ShouldProcessPii(channel *config.Channel) constants.MessagePiiOption {
//
//	// 检查 STT 服务是否启用
//	if nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.PII.Switch != "open" {
//		return constants.MessagePiiOptionServiceDisable
//	}
//	// 检查 tenant 是否开启 PII
//	if channel.ServiceEnable.PII != "open" {
//		return constants.MessagePiiOptionServiceTenantNoPermission
//	}
//	//如果是PII结果 则不需要再处理
//	if strings.HasPrefix(message.ID, constants.MessageIdPiiPrefix) {
//		return constants.MessagePiiOptionMessageTypeNotSupported
//	}
//	// 检查文件类型是否支持
//	supportedType := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.PII.SupportedFileExt
//	// type condition
//	switch message.Type {
//	case constants.WhatsAppMessageTypeImage:
//		//whatsApp过来没有fileName
//		if message.Image.FileName == "" {
//			// 获取拓展名
//			mExt := getFileExtensionFromMime(message.Image.MimeType)
//			if mExt != "" && strings.Contains(supportedType, mExt) {
//				return constants.MessagePiiOptionOK
//			}
//		}
//		return constants.MessagePiiOptionFileNotSupported
//	case constants.WhatsAppMessageTypeDocument:
//		if message.Document.FileName == "" {
//			// 获取拓展名
//			mExt := getFileExtensionFromMime(message.Document.MimeType)
//			if mExt != "" && strings.Contains(supportedType, mExt) {
//				return constants.MessagePiiOptionOK
//			}
//		}
//	case constants.WhatsAppMessageTypeSticker:
//		if message.Sticker.FileName == "" {
//			// 获取拓展名
//			mExt := getFileExtensionFromMime(message.Sticker.MimeType)
//			if mExt != "" && strings.Contains(supportedType, mExt) {
//				return constants.MessagePiiOptionOK
//			}
//		}
//	default:
//		return constants.MessagePiiOptionMessageTypeNotSupported
//	}
//	return constants.MessagePiiOptionFileNotSupported
//}
//
//func (message *Message) VirusScanCallbackUrl(ctx context.Context, mainMessageId string) *string {
//	channel := auth.GetChannelConfig(ctx)
//
//	_, attachmentPayload := message.ConvertTextNAttachment()
//
//	originalMessageId := message.ID
//	// query format
//	qeName := url.QueryEscape(message.FromName)
//	qeFileName := url.QueryEscape(*attachmentPayload.Filename)
//	qeMime := url.QueryEscape(*attachmentPayload.Mime)
//	qeMediaType := url.QueryEscape(message.Type)
//	qePHId := url.QueryEscape(mainMessageId)
//
//	// call back url
//	callbackUrl := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.VirusScan.Callback + "/" + channel.Tenant + "/" + string(constants.MessageProviderWhatsApp) + "/" + channel.Platforms.Whatsapp.ID + "/" + originalMessageId
//	// 拼接自己需要的参数
//	callbackUrl += fmt.Sprintf("?userId=%s&userName=%s&fileName=%s&mime=%s&mediaType=%s&placeholderId=%s&stt=%d&pii=%d", message.From, qeName, qeFileName, qeMime, qeMediaType, qePHId, message.Stt, message.Pii)
//	if logConfig, ok := storage.GetLogConfig(ctx); ok && (*logConfig)["traceId"] != "" {
//		qeTraceId := url.QueryEscape((*logConfig)["traceId"])
//		callbackUrl += "&traceId=" + qeTraceId
//	}
//	return &callbackUrl
//}
//
//func (message *Message) SttCallbackUrl(ctx context.Context, originalMessageId string) *string {
//	channel := auth.GetChannelConfig(ctx)
//	logConfig, logOk := storage.GetLogConfig(ctx)
//	if !logOk {
//		return nil
//	}
//
//	_, attachmentPayload := message.ConvertTextNAttachment()
//
//	num := uuid.New().String()
//	ext := filepath.Ext(*attachmentPayload.Filename)
//
//	// 去除空格和特殊字符
//	uploadFileName := utils.RemoveSpacesAndSpecialChars(num + ext)
//
//	directory := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.STT.Directory
//	filePath := path.Join(directory, uploadFileName)
//
//	// query format
//	qeFilePath := url.QueryEscape(filePath)
//	qeName := url.QueryEscape(message.FromName)
//
//	// call back url
//	callbackUrl := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.STT.Callback + "/" + channel.Tenant + "/" + string(constants.MessageProviderWhatsApp) + "/" + channel.Platforms.Whatsapp.ID + "/" + originalMessageId
//	callbackUrl += fmt.Sprintf("?userId=%s&userName=%s&filepath=%s", message.From, qeName, qeFilePath)
//	if (*logConfig)["traceId"] != "" {
//		qeTraceId := url.QueryEscape((*logConfig)["traceId"])
//		callbackUrl += "&traceId=" + qeTraceId
//	}
//	return &callbackUrl
//}
//func (message *Message) PiiCallbackUrl(ctx context.Context, originalMessageId string, mainMessageId string) *string {
//	channel := auth.GetChannelConfig(ctx)
//	logConfig, logOk := storage.GetLogConfig(ctx)
//	if !logOk {
//		return nil
//	}
//
//	textBody, attachmentPayload := message.ConvertTextNAttachment()
//
//	num := uuid.New().String()
//	ext := filepath.Ext(*attachmentPayload.Filename)
//
//	// 去除空格和特殊字符
//	uploadFileName := utils.RemoveSpacesAndSpecialChars(num + ext)
//
//	directory := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.PII.Directory
//	filePath := path.Join(directory, uploadFileName)
//
//	// query format
//	qeFilePath := url.QueryEscape(filePath)
//	qeName := url.QueryEscape(message.FromName)
//	qeFileName := url.QueryEscape(*attachmentPayload.Filename)
//	qeMime := url.QueryEscape(*attachmentPayload.Mime)
//	qeMediaType := url.QueryEscape(*attachmentPayload.MediaType)
//	qeCaption := url.QueryEscape(textBody)
//	qePHId := url.QueryEscape(mainMessageId)
//
//	// call back url
//	callbackUrl := nacos.ProjectConfig.TenantConfigs[channel.Tenant].ServiceSettings.PII.Callback + "/" + channel.Tenant + "/" + string(constants.MessageProviderWhatsApp) + "/" + channel.Platforms.Whatsapp.ID + "/" + originalMessageId
//	// 拼接自己需要的参数
//	callbackUrl += fmt.Sprintf("?userId=%s&userName=%s&filepath=%s&fileName=%s&mime=%s&mediaType=%s&caption=%s&placeholderId=%s", message.From, qeName, qeFilePath, qeFileName, qeMime, qeMediaType, qeCaption, qePHId)
//	if (*logConfig)["traceId"] != "" {
//		qeTraceId := url.QueryEscape((*logConfig)["traceId"])
//		callbackUrl += "&traceId=" + qeTraceId
//	}
//	return &callbackUrl
//}
//
//// getTypeFromMime : 尝试从 "mime":"audio/ogg; codecs=opus", 获取到 扩展名 ogg
//func getFileExtensionFromMime(mime string) string {
//	parts := strings.Split(mime, ";")
//	if len(parts) == 0 {
//		fmt.Println("No data before the semicolon")
//		return ""
//	}
//
//	// 取第一部分，并按照斜杠分割
//	subParts := strings.Split(parts[0], "/")
//	if len(subParts) < 2 {
//		fmt.Println("No valid media type found")
//		return ""
//	}
//	return subParts[1]
//}
//func getFileExtensionFromName(filename string) *string {
//	fileExtensions := strings.Split(filename, ".")
//	if len(fileExtensions) > 0 {
//		ext := fileExtensions[len(fileExtensions)-1]
//		return &ext
//	}
//	return nil
//}
