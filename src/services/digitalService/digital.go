package digitalService

import (
	"ctint-cdss-message/src/dab"
	dabo "ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto/inboundDTO"
	"ctint-cdss-message/src/services/digitalService/transit"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/kuah/cerror"
)

// ProcessTransitMessage 处理transit message并转换为dab message存入数据库
func ProcessTransitMessage(req *inboundDTO.TransitMessageRequest) (*inboundDTO.TransitMessageResponse, error) {
	if req == nil {
		return nil, cerror.New("request cannot be nil", 400)
	}

	if req.Tenant == "" {
		return nil, cerror.New("tenant is required", 400)
	}

	// 转换transit.Message到dabo.Message
	dabMessage, err := convertTransitMessageToDABMessage(&req.Message, req.Tenant)
	if err != nil {
		return nil, cerror.Wrap(err, "failed to convert transit message to dab message", 500)
	}

	// 插入数据库
	result, err := dabMessage.Insert(dab.MessageGraphEndPoint(req.Tenant))
	if err != nil {
		return nil, cerror.Wrap(err, "failed to insert message to database", 500)
	}

	// 返回响应
	response := &inboundDTO.TransitMessageResponse{
		MessageID: *result.ID,
		Status:    "success",
	}

	return response, nil
}

// convertTransitMessageToDABMessage 将transit.Message转换为dabo.Message
func convertTransitMessageToDABMessage(transitMsg *transit.Message, tenant string) (*dabo.Message, error) {
	if transitMsg == nil {
		return nil, cerror.New("transit message cannot be nil", 400)
	}

	// 生成UUID作为主键
	messageID := uuid.New().String()
	currentTime := time.Now().UTC()
	createBy := "digital-service"

	// 设置基本字段
	dabMessage := &dabo.Message{
		ID:         &messageID,
		Tenant:     &tenant,
		CreateTime: &currentTime,
		UpdateTime: &currentTime,
		CreateBy:   &createBy,
		UpdateBy:   &createBy,
	}

	// 转换基本消息字段
	if transitMsg.ID != "" {
		dabMessage.PlatformMessageID = &transitMsg.ID
		dabMessage.ExternalMessageID = &transitMsg.ID
	}

	if transitMsg.From != "" {
		dabMessage.FromAddress = &transitMsg.From
	}

	if transitMsg.FromName != "" {
		dabMessage.FromName = &transitMsg.FromName
	}

	if transitMsg.Type != "" {
		dabMessage.Type = &transitMsg.Type
	}

	// 处理平台信息
	if transitMsg.To != nil {
		platform := transitMsg.To.Platform
		dabMessage.Platform = &platform
		dabMessage.OriginalPlatform = &platform
		dabMessage.ToAddress = &transitMsg.To.Address
	}

	// 处理时间戳
	if transitMsg.Timestamp != "" {
		if timestamp, err := time.Parse(time.RFC3339, transitMsg.Timestamp); err == nil {
			dabMessage.Timestamp = &timestamp
		}
	}

	// 处理文本内容
	if transitMsg.Text != nil && transitMsg.Text.Body != "" {
		dabMessage.TextBody = &transitMsg.Text.Body
	}

	// 设置消息方向 - 假设是inbound
	direction := "inbound"
	dabMessage.Direction = &direction

	// 设置消息类别
	category := "normal"
	dabMessage.Category = &category

	// 设置状态
	status := "received"
	dabMessage.Status = &status

	// 设置操作者
	operator := "digital-service"
	dabMessage.Operator = &operator

	// 处理访问渠道信息
	if transitMsg.AccessPlatform != nil {
		dabMessage.Platform = transitMsg.AccessPlatform
	}

	if transitMsg.PhoneNumberID != nil {
		dabMessage.ChannelID = transitMsg.PhoneNumberID
	}

	if transitMsg.SenderID != nil {
		dabMessage.UserID = transitMsg.SenderID
		dabMessage.UserName = transitMsg.SenderID
	}

	// 设置服务配置
	if transitMsg.VirusScan > 0 {
		dabMessage.VirusScan = &transitMsg.VirusScan
	}
	if transitMsg.Stt > 0 {
		dabMessage.Stt = &transitMsg.Stt
	}
	if transitMsg.Pii > 0 {
		dabMessage.Pii = &transitMsg.Pii
	}
	if transitMsg.Saa > 0 {
		dabMessage.Saa = &transitMsg.Saa
	}

	// 处理原始载荷
	if rawPayload, err := json.Marshal(transitMsg); err == nil {
		rawPayloadStr := string(rawPayload)
		dabMessage.RawPayload = &rawPayloadStr
	}

	// 处理平台元数据
	if transitMsg.PlatformMetaData != nil {
		metadataStr := string(*transitMsg.PlatformMetaData)
		dabMessage.Metadata = &metadataStr
	}

	// 处理引用消息
	if transitMsg.ParentId != nil {
		dabMessage.ReferenceID = transitMsg.ParentId
		referenceType := "reply"
		dabMessage.ReferenceType = &referenceType
	}

	// 设置消息类型
	if transitMsg.Type != "" {
		messengerType := getMessengerType(transitMsg.Type)
		dabMessage.MessengerType = &messengerType
	}

	return dabMessage, nil
}

// getMessengerType 根据消息类型返回messenger类型
func getMessengerType(messageType string) string {
	switch messageType {
	case "text":
		return "whatsapp"
	case "image":
		return "whatsapp"
	case "video":
		return "whatsapp"
	case "audio":
		return "whatsapp"
	case "document":
		return "whatsapp"
	case "interactive":
		return "whatsapp"
	default:
		return "unknown"
	}
}
