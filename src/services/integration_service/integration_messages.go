package integration_service

import (
	"ctint-cdss-message/src/dab"
	"ctint-cdss-message/src/models/dto"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/logger"
	"fmt"
)

// CDSSMessagePaginationResult represents the paginated result of CDSS messages
type CDSSMessagePaginationResult struct {
	Items       []dto.CDSSMessage `json:"items"`
	EndCursor   *string           `json:"endCursor"`
	HasNextPage bool              `json:"hasNextPage"`
}

func QueryIntegrationMessages(tenant string, platform string, params dab.MessageQueryParams) (*CDSSMessagePaginationResult, error) {
	// Call dab layer to get messages
	dbResult, err := dab.GetMessagesByIntegration(tenant, platform, params)
	if err != nil {
		return nil, err
	}

	// Convert dab messages to CDSS messages
	var cdssMessages []dto.CDSSMessage
	for _, dbMessage := range dbResult.Items {
		cdssMessage, err := dbMessage.VO()
		if err != nil {
			logger.Error(fmt.Sprintf("failed to convert message to VO %s", *dbMessage.ID))
			continue
		}
		cdssMessages = append(cdssMessages, *cdssMessage)
	}

	// Handle HasNextPage pointer to bool conversion
	hasNextPage := false
	if dbResult.HasNextPage != nil {
		hasNextPage = *dbResult.HasNextPage
	}

	// Construct the result with pagination info
	result := &CDSSMessagePaginationResult{
		Items:       cdssMessages,
		EndCursor:   dbResult.EndCursor,
		HasNextPage: hasNextPage,
	}

	return result, nil
}