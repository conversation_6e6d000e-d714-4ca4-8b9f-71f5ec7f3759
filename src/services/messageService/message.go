package messageService

import (
	"ctint-cdss-message/src/constants"
	ecs "ctint-cdss-message/src/constants/errorCodes"
	"ctint-cdss-message/src/dab"
	dabo "ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/services/gcService"
	"github.com/kuah/cerror"
)

// CDSSMessageContext T 决定了里面payload 的类型
type CDSSMessageContext[T any] struct {
	// gc message 需要用到 上下文
	gcService.MessageContext[T]
	Reference *dto.MessageReference
	// only for 入库用
	UserID        string
	Username      string
	ParticipantID string
	Trace         dto.CtintR
}

func ValidReference(tenant, conversationId, messageId string, message **dabo.Message) error {
	results, err := dab.GetMessagesByMessageId(tenant, constants.GCPlatformIdentifier, messageId)
	if err != nil {
		return cerror.New("failed to find reference", ecs.OutboundErrorCodesFailedToFindReference)
	}
	if len(results) != 1 {
		return cerror.New("failed to find reference", ecs.OutboundErrorCodesFailedToFindReference)
	}
	target := results[0]
	if target.OriginalPlatform == nil || *target.OriginalPlatform == "" {
		return cerror.New("referenced message is missing original platform", ecs.OutboundErrorCodesReferencedMessageMissingOriginalPlatform)
	}
	if target.Status != nil && (*target.Status == constants.MessageOutBoundStatusFailed || *target.Status == constants.MessageOutBoundStatusQueued) {
		return cerror.New("referenced message status invalid", ecs.OutboundErrorCodesReferencedMessageStatusInvalid)
	}
	if target.ExternalMessageID == nil || *target.ExternalMessageID == "" {
		return cerror.New("referenced message is missing external message id", ecs.OutboundErrorCodesReferencedMessageMissingExternalMessageId)
	}
	if target.ConversationID == nil || *target.ConversationID != conversationId {
		return cerror.New("reference message is not in the same conversation", ecs.OutboundErrorCodesReferencedMessageNotInCorrectConversation)
	}
	*message = &target
	return nil
}
