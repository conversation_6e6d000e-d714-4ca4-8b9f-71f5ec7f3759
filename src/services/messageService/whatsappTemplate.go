package messageService

import (
	"ctint-cdss-message/src/constants"
	ecs "ctint-cdss-message/src/constants/errorCodes"
	"ctint-cdss-message/src/dab"
	dabo "ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/models/dto/outboundDTO"
	utils "ctint-cdss-message/src/pkg"
	"ctint-cdss-message/src/services/flowService"
	"ctint-cdss-message/src/services/gcService"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/logger"
	"fmt"
	"github.com/kuah/cerror"
	"github.com/pkg/errors"
	"net/http"
	"strings"
)

// SendOutboundCDSSWhatsAppTemplateMessage send whatsapp interactive message
func SendOutboundCDSSWhatsAppTemplateMessage(ctx CDSSMessageContext[outboundDTO.CDSSWhatsappTemplateMessagePayload]) (*dabo.Message, error) {
	//验证reference(若有)
	var refMessage *dabo.Message
	if ctx.Reference != nil {
		err := ValidReference(ctx.Tenant, ctx.ConversationId, ctx.Reference.MessageId, &refMessage)
		if err != nil {
			return nil, err
		}
	}
	//// step 2 :  先发送真实的消息, 留底
	//// step 2.1 : 先解释有多少个 message 要发送
	preview, err := convertPreviewToGCDisplayItem(*ctx.Payload.Preview)
	if err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppTemplateMessage : failed to convert preview to gc display item")
	}

	// step 2.2 : 按规则,发出openMessage 留底
	//按顺序留底
	gcIDs, gcMessage, medias, err := sendCombinedGCDisplayOfWhatsAppTemplateMessage(gcService.MessageContext[outboundDTO.WhatsappTemplateMessageGCDisplay]{
		GCMessageEndPointContext: ctx.GCMessageEndPointContext,
		Payload:                  preview,
	})
	if err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppTemplateMessage : failed to send outbound message")
	}

	// step 3 : 获取当前的conversation的integration
	to := ctx.Payload.Payload.To

	if gcMessage == nil {
		return nil, errors.New("SendOutboundCDSSWhatsAppTemplateMessage : unknown response from gc backup message")
	}
	integrationId := gcMessage.NormalizedMessage.Channel.From.ID
	phoneNumber := gcMessage.NormalizedMessage.Channel.To.ID
	if to == nil {
		ctx.Payload.Payload.To = &phoneNumber
	}

	// step 4 : 发送cdss message
	messageId := utils.GenerateUniqueID(constants.GCMessageIDWhatsAppTemplatePrefix, "") + ctx.ConversationId
	if messageId == "" {
		return nil, cerror.New("SendOutboundCDSSMessageByConversation : failed to gen message Id", ecs.OutboundErrorCodesGenUUID)
	}

	// 4.1 : insert into db before send out the cdss message
	dabRecord, err := dab.InsertTemplateMessage(messageId, ctx.Tenant, constants.GCPlatformIdentifier, ctx.ConversationId, ctx.UserID, ctx.Username, ctx.ParticipantID, *gcMessage, medias, ctx.Payload, ctx.Reference, ctx.MetaData)
	if err != nil || dabRecord == nil {
		return nil, errors.New("SendOutboundCDSSMessageByConversation : failed to insert message into cdss db")
	}
	//
	// 4.2 顺手保存 message 的relation
	go func(tenant, platform, messageId, userId string, platformMessageIDs []string) {
		_, _ = dab.InsertMessageRelation(tenant, platform, messageId, userId, platformMessageIDs)
	}(ctx.Tenant, constants.GCPlatformIdentifier, messageId, ctx.UserID, gcIDs)

	mainMessagePayload := dto.CDSSOutboundDataActionPayload{
		IntegrationId:   integrationId,
		MessageID:       messageId,
		Payload:         ctx.Payload.Payload,
		SubMessageIDs:   gcIDs,
		Type:            constants.CDSSMessageTypeTemplate,
		Platform:        string(constants.JWTPlatformGenesysCloud),
		PlatformAccount: integrationId,
	}
	if refMessage != nil && ctx.Reference != nil {
		mainMessagePayload.Reference = &dto.OutboundMessageReference{
			MessageReference: dto.MessageReference{
				MessageId: *refMessage.ExternalMessageID,
				Type:      ctx.Reference.Type,
			},
			OriginalPlatform: *refMessage.OriginalPlatform,
		}
	}
	//_, err = gcService.SendOutboundDataActionMessage(ctx.Ctx, ctx.Tenant, ctx.Token, config.ProjectConfig.GenesysCloud.OutboundDataActionId, mainMessagePayload, true)
	_, err = flowService.SendOutboundMessageToProxy(ctx.Ctx, ctx.Tenant, ctx.Token, mainMessagePayload, ctx.Trace, mainMessagePayload.IntegrationId)
	//2.5 如果报错,  更新数据库
	if err != nil {
		returnErr := errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to send data action")
		errorMessage := returnErr.Error()
		logger.Error(errorMessage)
		status := constants.MessageOutBoundStatusFailed
		cause := fmt.Sprintf("%d", ecs.MessageErrorCodesFailedToCallDataAction)
		dabRecord.Status = &status
		dabRecord.FailCause = &cause
		_, _ = dab.UpdateMessageStatus(ctx.Tenant, constants.GCPlatformIdentifier, messageId, constants.MessageOutBoundStatusFailed, "", cause, "")

		return dabRecord, returnErr
	}
	return dabRecord, nil
}

// 将前端提交的preview参数, 转化成准备留底GC的structure
func convertPreviewToGCDisplayItem(preview outboundDTO.WhatsappTemplateMessagePreview) (outboundDTO.WhatsappTemplateMessageGCDisplay, error) {

	displayMap := make(outboundDTO.WhatsappTemplateMessageGCDisplay)
	for _, item := range preview.Components {
		var displayItems []outboundDTO.WhatsappTemplateMessageGCDisplayItem
		switch {
		case strings.HasPrefix(item.Type, "BUTTONS") && item.Buttons != nil && len(*item.Buttons) > 0:
			for _, btn := range *item.Buttons {
				text := btn.Text
				if btn.Url != "" {
					text = text + "(" + btn.Url + ")"
				}
				displayItems = append(displayItems, outboundDTO.WhatsappTemplateMessageGCDisplayItem{
					GroupType: btn.Type,
					Type:      btn.Type,
					Text:      text,
				})
			}
		case item.Type == "HEADER":
			if item.Format == "IMAGE" {
				if item.Image == nil || item.Image.Link == nil || item.Image.Id == nil {
					return nil, cerror.New("Invalid request body : invalid params in IMAGE component .image", http.StatusBadRequest)
				}
				displayItems = append(displayItems, outboundDTO.WhatsappTemplateMessageGCDisplayItem{
					GroupType: item.Type,
					Type:      item.Format,
					Link:      *item.Image.Link,
					MediaId:   *item.Image.Id,
				})
			} else {
				displayItems = append(displayItems, outboundDTO.WhatsappTemplateMessageGCDisplayItem{
					GroupType: item.Type,
					Type:      item.Format,
					Text:      *item.Text,
				})
			}
		default:
			if item.Text != nil {
				text := *item.Text
				displayItems = append(displayItems, outboundDTO.WhatsappTemplateMessageGCDisplayItem{
					GroupType: item.Type,
					Type:      "TEXT",
					Text:      text,
				})
			} else {
				return nil, cerror.New("Invalid request body : invalid params in TEXT component .text", http.StatusBadRequest)
			}
		}
		displayMap[item.Type] = displayItems
	}
	return displayMap, nil

}

// sendCombinedGCDisplayOfWhatsAppTemplateMessage 除了图片, 其他components 全部combine 然后在GC上留底
func sendCombinedGCDisplayOfWhatsAppTemplateMessage(ctx gcService.MessageContext[outboundDTO.WhatsappTemplateMessageGCDisplay]) ([]string, *outboundDTO.OutboundMessageResponseMessage, []outboundDTO.OutboundMessageResponseMedia, error) {
	var gcIDs []string
	medias := make([]outboundDTO.OutboundMessageResponseMedia, 0)
	textBody := "[WhatsAppTemplateMessage]\n"
	components := []string{"HEADER", "BODY", "FOOTER", "BUTTONS"}
	for _, comType := range components {
		if items, ok := ctx.Payload[comType]; ok {
			for _, item := range items {
				switch item.Type {
				case "IMAGE":
					line := "[" + item.GroupType + "-" + item.Type + "] : \n" + "PLEASE REFER TO THE IMAGE AS ABOVE"
					textBody = textBody + line + "\n"
					gcId, media, err := gcService.SendGCImageMessage(ctx.GCMessageEndPointContext, item.MediaId)
					if err != nil {
						return nil, nil, nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppInteractiveMessage : failed to backup outbound media message")
					} else {
						medias = append(medias, *media)
						gcIDs = append(gcIDs, *gcId)
					}
				default:
					if item.Text != "" {
						line := "[" + item.GroupType + "-" + item.Type + "] : \n" + item.Text
						textBody = textBody + line + "\n"
					}
				}
			}
		}
	}
	payload := make(map[string]interface{})
	payload["textBody"] = textBody
	resp, err := gcService.SendGCOutboundOpenMessage(gcService.MessageContext[any]{
		GCMessageEndPointContext: ctx.GCMessageEndPointContext,
		Payload:                  payload,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	var messageResp outboundDTO.OutboundMessageResponseMessage
	if err := utils.DecodeHTTPResponse(resp, &messageResp); err != nil {
		return nil, nil, nil, errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to decode response body")
	}
	gcIDs = append(gcIDs, messageResp.ID)
	return gcIDs, &messageResp, medias, nil
}
