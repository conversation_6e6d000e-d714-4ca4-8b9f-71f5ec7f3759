package messageService

import (
	"context"
	"ctint-cdss-message/src/constants"
	ecs "ctint-cdss-message/src/constants/errorCodes"
	"ctint-cdss-message/src/dab"
	dabo "ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/models/dto/outboundDTO"
	"ctint-cdss-message/src/models/vo"
	utils "ctint-cdss-message/src/pkg"
	"ctint-cdss-message/src/services/flowService"
	"ctint-cdss-message/src/services/gcService"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/logger"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/kuah/cerror"
	"github.com/pkg/errors"
	"strings"
)

func GetInteractiveTemplates(ctx context.Context, tenant string, platform string, category *string, name *string, interactiveType *string) ([]vo.MessageInteractiveTemplateResp, error) {
	results, err := dab.GetInteractiveTemplates(tenant, platform, category, interactiveType, name)
	if err != nil {
		return nil, errors.Wrap(err, "GetInteractiveTemplates : failed to get interactive templates")
	}
	// convert the interactive template payload structure for resp
	var resp []vo.MessageInteractiveTemplateResp
	for _, iTemplate := range results {
		respIT := vo.MessageInteractiveTemplateResp{
			ID:         iTemplate.ID,
			Tenant:     iTemplate.Tenant,
			Platform:   iTemplate.Platform,
			Category:   iTemplate.Category,
			Type:       iTemplate.Type,
			CreateTime: iTemplate.CreateTime,
			CreateBy:   iTemplate.CreateBy,
			UpdateTime: iTemplate.UpdateTime,
			UpdateBy:   iTemplate.UpdateBy,
			Name:       iTemplate.Name,
		}
		if iTemplate.Payload != nil {
			var payloadMap map[string]interface{}
			err := json.Unmarshal([]byte(*iTemplate.Payload), &payloadMap)
			if err != nil {
				continue
			}
			respIT.Payload = &payloadMap
		}
		resp = append(resp, respIT)
	}
	return resp, nil
}

// SendOutboundCDSSWhatsAppInteractiveMessage 发送 interactive message
func SendOutboundCDSSWhatsAppInteractiveMessage(ctx CDSSMessageContext[outboundDTO.CDSSWhatsappInteractiveMessagePayload]) (*dabo.Message, error) {
	//验证reference(若有)
	var refMessage *dabo.Message
	if ctx.Reference != nil {
		err := ValidReference(ctx.Tenant, ctx.ConversationId, ctx.Reference.MessageId, &refMessage)
		if err != nil {
			return nil, err
		}
	}
	// step 1 因为interactive template, 用base64 存储,在发送的时候, 再将图片发送到conversation 中, 这样, 需要对template 中的base64做预处理
	processedMessageCtx, err := preprocessMediaOfInteractive(ctx.MessageContext)
	if err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppInteractiveMessage : failed to preprocess media")
	}
	ctx.MessageContext = processedMessageCtx

	// step 2 : 按规则,发出openMessage 留底
	//按顺序留底
	gcIDs, gcMessage, medias, err := sendCombinedGCDisplayOfWhatsAppInteractiveMessage(ctx.MessageContext)
	if err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppInteractiveMessage : failed to send outbound message")
	}

	// step 3 : 获取当前的conversation的integration
	to := ctx.Payload.Payload.To

	if gcMessage == nil {
		return nil, errors.New("SendOutboundCDSSWhatsAppInteractiveMessage : unknown response from gc backup message")
	}
	integrationId := gcMessage.NormalizedMessage.Channel.From.ID
	phoneNumber := gcMessage.NormalizedMessage.Channel.To.ID
	if to == nil {
		ctx.Payload.Payload.To = &phoneNumber
	}

	// step 4 : 发送cdss message
	messageId := utils.GenerateUniqueID(constants.GCMessageIDWhatsAppInteractivePrefix, "") + ctx.ConversationId
	if messageId == "" {
		return nil, cerror.New("SendOutboundCDSSMessageByConversation : failed to gen message Id", ecs.OutboundErrorCodesGenUUID)
	}

	// 4.1 : insert into db before send out the cdss message
	dabRecord, err := dab.InsertInteractiveMessage(messageId, ctx.Tenant, constants.GCPlatformIdentifier, ctx.ConversationId, ctx.UserID, ctx.Username, ctx.ParticipantID, *gcMessage, medias, ctx.Payload, ctx.Reference, ctx.MetaData)
	if err != nil || dabRecord == nil {
		return nil, errors.New("SendOutboundCDSSMessageByConversation : failed to insert message into cdss db")
	}
	//
	// 4.2 顺手保存 message 的relation
	go func(tenant, platform, messageId, userId string, platformMessageIDs []string) {
		_, _ = dab.InsertMessageRelation(tenant, platform, messageId, userId, platformMessageIDs)
	}(ctx.Tenant, constants.GCPlatformIdentifier, messageId, ctx.UserID, gcIDs)

	mainMessagePayload := dto.CDSSOutboundDataActionPayload{
		IntegrationId:   integrationId,
		MessageID:       messageId,
		Payload:         ctx.Payload.Payload,
		SubMessageIDs:   gcIDs,
		Type:            constants.CDSSMessageTypeInteractive,
		Platform:        string(constants.JWTPlatformGenesysCloud),
		PlatformAccount: integrationId,
	}
	if refMessage != nil && ctx.Reference != nil {
		mainMessagePayload.Reference = &dto.OutboundMessageReference{
			MessageReference: dto.MessageReference{
				MessageId: *refMessage.ExternalMessageID,
				Type:      ctx.Reference.Type,
			},
			OriginalPlatform: *refMessage.OriginalPlatform,
		}
	}
	//_, err = gcService.SendOutboundDataActionMessage(ctx.Ctx, ctx.Tenant, ctx.Token, config.ProjectConfig.GenesysCloud.OutboundDataActionId, mainMessagePayload, true)
	_, err = flowService.SendOutboundMessageToProxy(ctx.Ctx, ctx.Tenant, ctx.Token, mainMessagePayload, ctx.Trace, mainMessagePayload.IntegrationId)

	//2.5 如果报错,  更新数据库
	if err != nil {
		returnErr := errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to send data action")
		errorMessage := returnErr.Error()
		logger.Error(errorMessage)
		status := constants.MessageOutBoundStatusFailed
		cause := fmt.Sprintf("%d", ecs.MessageErrorCodesFailedToCallDataAction)
		dabRecord.Status = &status
		dabRecord.FailCause = &cause
		_, _ = dab.UpdateMessageStatus(ctx.Tenant, constants.GCPlatformIdentifier, messageId, constants.MessageOutBoundStatusFailed, "", cause, "")

		return dabRecord, returnErr
	}
	return dabRecord, nil
}

// preprocessMediaOfInteractive 因为interactive template, 用base64 存储,在发送的时候, 再将图片发送到conversation 中, 这样, 需要对template 中的base64做预处理
func preprocessMediaOfInteractive(ctx gcService.MessageContext[outboundDTO.CDSSWhatsappInteractiveMessagePayload]) (gcService.MessageContext[outboundDTO.CDSSWhatsappInteractiveMessagePayload], error) {
	if ctx.Payload.Payload.Interactive.Header != nil && ctx.Payload.Payload.Interactive.Header.Image != nil && ctx.Payload.Payload.Interactive.Header.Image.Data != nil {
		mediaInfo, err := gcService.UploadBase64MediaToGC(ctx.GCMessageEndPointContext, uuid.New().String(), *ctx.Payload.Payload.Interactive.Header.Image.Data)
		if err != nil {
			return ctx, errors.Wrap(err, "SendOutboundCDSSWhatsAppInteractiveMessage : failed to upload base64 image when backup outbound media message")
		}
		ctx.Payload.Payload.Interactive.Header.Image.Data = nil
		ctx.Payload.Payload.Interactive.Header.Image.Link = &mediaInfo.URL
		if ctx.Payload.MediaMapping == nil {
			ctx.Payload.MediaMapping = &map[string]string{}
		}
		(*ctx.Payload.MediaMapping)[mediaInfo.URL] = mediaInfo.ID
		return ctx, nil
	}
	return ctx, nil
}

// sendCombinedGCDisplayOfWhatsAppInteractiveMessage 除了图片, 其他components 全部combine 然后在GC上留底
func sendCombinedGCDisplayOfWhatsAppInteractiveMessage(ctx gcService.MessageContext[outboundDTO.CDSSWhatsappInteractiveMessagePayload]) ([]string, *outboundDTO.OutboundMessageResponseMessage, []outboundDTO.OutboundMessageResponseMedia, error) {
	if ctx.Payload.Payload.Type != nil && strings.Contains(dto.CDSSMessageTypeWhatsappInteractive, *ctx.Payload.Payload.Type) {
		return nil, nil, nil, errors.New("SendOutboundCDSSWhatsAppInteractiveMessage : invalid payload type")
	}
	var gcIDs []string
	medias := make([]outboundDTO.OutboundMessageResponseMedia, 0)
	textBody := "[WhatsAppInteractiveMessage]\n"
	// header
	if ctx.Payload.Payload.Interactive.Header != nil {
		switch *ctx.Payload.Payload.Interactive.Header.Type {
		case "image":
			line := "[" + "IMAGE" + "] : \n" + "PLEASE REFER TO THE IMAGE AS ABOVE"
			textBody = textBody + line + "\n"

			if ctx.Payload.Payload.Interactive.Header.Image.Link != nil {
				link := *ctx.Payload.Payload.Interactive.Header.Image.Link
				if gcMediaId, ok := (*ctx.Payload.MediaMapping)[link]; ok && link != "" {
					gcId, media, err := gcService.SendGCImageMessage(ctx.GCMessageEndPointContext, gcMediaId)
					if err != nil {
						return nil, nil, nil, errors.Wrap(err, "SendOutboundCDSSWhatsAppInteractiveMessage : failed to backup outbound media message")
					} else {
						medias = append(medias, *media)
						gcIDs = append(gcIDs, *gcId)
					}
				}
			}
		case "text":
			line := "[" + "TEXT" + "] : \n" + *ctx.Payload.Payload.Interactive.Header.Text
			textBody = textBody + line + "\n"
		}
	}
	// body
	if ctx.Payload.Payload.Interactive.Body != nil {
		line := "[" + "TEXT" + "] : \n" + *ctx.Payload.Payload.Interactive.Body.Text
		textBody = textBody + line + "\n"
	}
	// footer
	if ctx.Payload.Payload.Interactive.Footer != nil {
		line := "[" + "TEXT" + "] : \n" + *ctx.Payload.Payload.Interactive.Footer.Text
		textBody = textBody + line + "\n"
	}
	switch *ctx.Payload.Payload.Interactive.Type {
	case "list":
		// sections
		for _, section := range *ctx.Payload.Payload.Interactive.Action.Sections {
			lineS := "[" + "SECTIONS" + "] : \n" + *section.Title
			textBody = textBody + lineS + "\n"
			for _, row := range *section.Rows {
				lineR := "[" + "ROWS" + "] : \n" + *row.Title + "(" + *row.Description + ")"
				textBody = textBody + lineR + "\n"
			}
		}
		// button
		line := "[" + "BUTTON" + "] : \n" + *ctx.Payload.Payload.Interactive.Action.Button
		textBody = textBody + line + "\n"

	case "button":
		// buttons
		for _, button := range *ctx.Payload.Payload.Interactive.Action.Buttons {
			switch *button.Type {
			case "reply":
				line := "[" + "BUTTON - REPLY" + "] : \n" + *button.Reply.Title
				textBody = textBody + line + "\n"
			}
		}
	case "cta_url":
		switch *ctx.Payload.Payload.Interactive.Action.Name {
		case "cta_url":
			line := "[" + "BUTTON - URL" + "] : \n" + *ctx.Payload.Payload.Interactive.Action.Parameters.DisplayText + "(" + *ctx.Payload.Payload.Interactive.Action.Parameters.URL + ")"
			textBody = textBody + line + "\n"
		}
	}
	payload := make(map[string]interface{})
	payload["textBody"] = textBody
	resp, err := gcService.SendGCOutboundOpenMessage(gcService.MessageContext[any]{
		GCMessageEndPointContext: ctx.GCMessageEndPointContext,
		Payload:                  payload,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	var messageResp outboundDTO.OutboundMessageResponseMessage
	if err := utils.DecodeHTTPResponse(resp, &messageResp); err != nil {
		return nil, nil, nil, errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to decode response body")
	}
	gcIDs = append(gcIDs, messageResp.ID)
	return gcIDs, &messageResp, medias, nil
}
