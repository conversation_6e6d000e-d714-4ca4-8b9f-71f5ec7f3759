package messageService

import (
	"ctint-cdss-message/src/constants"
	ecs "ctint-cdss-message/src/constants/errorCodes"
	"ctint-cdss-message/src/dab"
	"ctint-cdss-message/src/dab/dabObjects"
	"ctint-cdss-message/src/models/dto"
	"ctint-cdss-message/src/models/dto/outboundDTO"
	utils "ctint-cdss-message/src/pkg"
	"ctint-cdss-message/src/services/flowService"
	"ctint-cdss-message/src/services/gcService"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-common-lib.git/common/logger"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"net/http"
)

// SendOutboundCDSSMessage cdss 消息分三步骤发送outbound message  1. 在GC 上面留底 2. 保存到数据库, 3.发送data action
func SendOutboundCDSSMessage(ctx CDSSMessageContext[interface{}]) (*dabo.Message, error) {

	//验证reference(若有)
	var refMessage *dabo.Message
	if ctx.Reference != nil {
		err := ValidReference(ctx.Tenant, ctx.ConversationId, ctx.Reference.MessageId, &refMessage)
		if err != nil {
			return nil, err
		}
	}
	// step 1 :  先发送一条真实的消息, 留底
	resp, err := gcService.SendGCOutboundOpenMessage(ctx.MessageContext)
	if err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to send original gc message")
	}
	utils.LogResponse(resp, http.MethodPost, "SendOutboundOpenMessage")

	// original message result
	var messageResp outboundDTO.OutboundMessageResponseMessage
	if err := utils.DecodeHTTPResponse(resp, &messageResp); err != nil {
		return nil, errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to decode response body in step 1")
	}
	// media 转换, 将外部的media 放到content 中
	for i, content := range messageResp.NormalizedMessage.Content {
		if content.Attachment != nil {
			for _, media := range messageResp.Media {
				if content.Attachment.ID == media.ID {
					//&media.MediaType
					messageResp.NormalizedMessage.Content[i].Attachment = &media
					break
				}
			}
		}
	}

	// step 2 : 发送cdss message
	// 2.1 : 获取integrationId
	integrationId := messageResp.NormalizedMessage.Channel.ID
	if integrationId == "" {
		return nil, errors.New("SendOutboundCDSSMessageByConversation : failed to get integrationId in GC message")
	}
	messageId := utils.GenerateUniqueID(constants.GCMessageIDNormalizedPrefix, "")
	if messageId == "" {
		return nil, errors.New("SendOutboundCDSSMessageByConversation : failed to gen message Id")
	}
	mainMessagePayload := dto.CDSSOutboundDataActionPayload{
		Type:            constants.CDSSMessageTypeNormal,
		MessageID:       messageId,
		Payload:         messageResp,
		SubMessageIDs:   []string{messageResp.ID},
		IntegrationId:   integrationId,
		Platform:        string(constants.JWTPlatformGenesysCloud),
		PlatformAccount: integrationId,
	}
	if refMessage != nil && ctx.Reference != nil {
		mainMessagePayload.Reference = &dto.OutboundMessageReference{
			MessageReference: dto.MessageReference{
				MessageId: *refMessage.ExternalMessageID,
				Type:      ctx.Reference.Type,
			},
			OriginalPlatform: *refMessage.OriginalPlatform,
		}
	}
	payloadJson, _ := json.Marshal(mainMessagePayload)

	// 2.2 : insert into db before send out the cdss message

	dabRecord, err := dab.InsertMessageByGCMessageResponse(messageId, ctx.Tenant, constants.GCPlatformIdentifier, string(payloadJson), ctx.UserID, ctx.Username, ctx.ParticipantID, messageResp, ctx.Reference, ctx.MetaData)
	if err != nil || dabRecord == nil {
		return nil, errors.New("SendOutboundCDSSMessageByConversation : failed to insert message into cdss db")
	}

	// 2.3 顺手保存 message 的relation
	go func(tenant, platform, messageId, userId string, platformMessageIDs []string) {
		_, _ = dab.InsertMessageRelation(tenant, platform, messageId, userId, platformMessageIDs)
	}(ctx.Tenant, constants.GCPlatformIdentifier, messageId, ctx.UserID, []string{messageResp.ID})

	// 2.4 send outbound data action
	//_, err = gcService.SendOutboundDataActionMessage(ctx.Ctx, ctx.Tenant, ctx.Token, config.ProjectConfig.GenesysCloud.OutboundDataActionId, mainMessagePayload, true)
	_, err = flowService.SendOutboundMessageToProxy(ctx.Ctx, ctx.Tenant, ctx.Token, mainMessagePayload, ctx.Trace, mainMessagePayload.IntegrationId)

	// 2.5 如果报错,  更新数据库
	if err != nil {
		returnErr := errors.Wrap(err, "SendOutboundCDSSMessageByConversation : failed to send data action")
		errorMessage := returnErr.Error()
		logger.Error(errorMessage)
		status := constants.MessageOutBoundStatusFailed
		cause := fmt.Sprintf("%d", ecs.MessageErrorCodesFailedToCallDataAction)
		dabRecord.Status = &status
		dabRecord.FailCause = &cause
		_, _ = dab.UpdateMessageStatus(ctx.Tenant, constants.GCPlatformIdentifier, messageId, constants.MessageOutBoundStatusFailed, "", cause, "")

		return dabRecord, returnErr
	}
	return dabRecord, nil
}
