service:
  name: ctint-cdss-message
  basepath: /ctint-cdss-message
  port: 8205
  version: v1.0.0
logger:
  maxSize: 100
  maxBackups: 10
  maxAge: 28
genesys-cloud:
  outboundDataActionId: custom_-_e0663eaf-3037-451d-abb8-fee3c7bb13dc
  externalAPIGatewayDataActionId: custom_-_50544b28-f0b2-4f32-9b50-308eb9ee73bd
#http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com/ctint/session-manager/service/boardcast
ws-broadcast:
  api : /service/boardcast
  # basic base64(ctint-session-manager:P@ssword)
  auth : "basic Y3RpbnQtc2Vzc2lvbi1tYW5hZ2VyOlBAc3N3b3Jk"
